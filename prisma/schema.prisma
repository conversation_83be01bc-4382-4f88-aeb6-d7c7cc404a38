generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model User {
  userId             Int                  @id @default(autoincrement())
  email              String               @unique
  passwordHash       String?
  createdAt          DateTime             @default(now())
  updatedAt          DateTime             @updatedAt
  isActive           Boolean?             @default(true)
  deletionReason     String?
  Report             Report[]
  userRoles          UserRole[]
  Business           Business?
  PotentialCandidate PotentialCandidate[]
  MagicLink          MagicToken[]
  DeviceSessions     DeviceSession[]
  roomAccesses       RoomAccess[]
  interviewRequests  InterviewRequest[]
  clientPermission   ClientPermission?
}

model DeviceSession {
  id         Int      @id @default(autoincrement())
  userId     Int
  deviceId   String // Unique identifier for the device
  deviceName String? // Optional device name (e.g., "iPhone 13")
  userAgent  String? // Browser/device user agent
  ipAddress  String? // IP address
  lastActive DateTime @default(now()) // Track last activity
  createdAt  DateTime @default(now())
  isActive   Boolean  @default(true)

  user User @relation(fields: [userId], references: [userId], onDelete: Cascade)

  @@unique([userId, deviceId])
}

model BlacklistedEmail {
  id        Int      @id @default(autoincrement())
  email     String   @unique
  reason    String?
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
}

model RoomAccess {
  roomAccessId String            @id @default(uuid())
  roomId       Int
  userId       Int
  isActive     Boolean           @default(true)
  expiresAt    DateTime
  createdAt    DateTime          @default(now())
  updatedAt    DateTime          @updatedAt
  room         CodingTestSection @relation(fields: [roomId], references: [codingTestSectionId], onDelete: Cascade)
  user         User              @relation(fields: [userId], references: [userId], onDelete: Cascade)

  @@unique([roomId, userId])
}

model Certification {
  certificationId   Int                 @id @default(autoincrement())
  name              String?
  issueDate         DateTime?
  proof             String?
  description       String?
  type              String?
  createdAt         DateTime            @default(now())
  updatedAt         DateTime            @updatedAt
  UserCertification UserCertification[]
}

model UserCertification {
  userId          Int
  roleId          Int
  certificationId Int
  score           String?
  issuer          String?
  attachmentUrl   String?
  issueDate       DateTime?
  expirationDate  DateTime?
  createdAt       DateTime      @default(now())
  updatedAt       DateTime      @updatedAt
  certification   Certification @relation(fields: [certificationId], references: [certificationId])
  userRole        UserRole      @relation(fields: [userId, roleId], references: [userId, roleId])

  @@id([userId, roleId, certificationId])
}

model Language {
  languageId       Int                @id @default(autoincrement())
  languageName     String
  isActive         Boolean?           @default(true)
  createdAt        DateTime           @default(now())
  updatedAt        DateTime           @updatedAt
  JobLanguageSkill JobLanguageSkill[]
  userLanguages    UserLanguage[]
}

model UserLanguage {
  userId                Int
  languageId            Int
  languageProficiencyId Int?
  roleId                Int
  createdAt             DateTime             @default(now())
  updatedAt             DateTime             @updatedAt
  languageProficiency   LanguageProficiency? @relation(fields: [languageProficiencyId], references: [languageProficiencyId])
  language              Language             @relation(fields: [languageId], references: [languageId])
  userRole              UserRole             @relation(fields: [userId, roleId], references: [userId, roleId])

  @@id([userId, roleId, languageId])
}

model Company {
  name                     String
  website                  String?
  description              String?
  location                 String?
  industryId               Int?
  companyId                Int               @id @default(autoincrement())
  createdAt                DateTime          @default(now())
  updatedAt                DateTime          @updatedAt
  receivedAffiliateRewards AffiliateReward[] @relation("referredCompanyId")
  sentAffiliateRewards     AffiliateReward[] @relation("referrerCompanyId")
  industry                 Industry?         @relation(fields: [industryId], references: [industryId])
  couponCodes              CouponCode[]
  profile                  Profile[]
}

model City {
  cityId            Int                 @id @default(autoincrement())
  countryId         Int
  name              String
  country           Country             @relation(fields: [countryId], references: [countryId])
  createdAt         DateTime            @default(now())
  updatedAt         DateTime            @updatedAt
  userCountry       UserCountry[]
  employmentHistory EmploymentHistory[]

  JobLocation JobLocation[]
}

model Country {
  countryId         Int                 @id @default(autoincrement())
  countryName       String              @unique
  cities            City[]
  flagImage         String
  createdAt         DateTime            @default(now())
  updatedAt         DateTime            @updatedAt
  code              String?
  Headquarter       MeetingUser[]
  profile           Profile[]
  Business          Business[]
  StateHeadquarter  StateHeadquarter[]
  userCountry       UserCountry[]
  employmentHistory EmploymentHistory[]

  JobLocation JobLocation[]
}

model UserCountry {
  userCountryId    Int  @id @default(autoincrement())
  workPreferenceId Int? @unique
  countryId        Int?
  cityId           Int?

  workPreference WorkPreference? @relation(fields: [workPreferenceId], references: [workPreferenceId])
  country        Country?        @relation(fields: [countryId], references: [countryId])
  city           City?           @relation(fields: [cityId], references: [cityId])

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
}

model Role {
  roleId    Int        @id @default(autoincrement())
  roleName  String     @unique
  userRoles UserRole[]
}

model PicHistory {
  picHistoryId  Int         @id @default(autoincrement())
  applicationId Int
  userId        Int
  roleId        Int
  description   String?
  createdAt     DateTime    @default(now())
  updatedAt     DateTime    @updatedAt
  application   Application @relation(fields: [applicationId], references: [applicationId])
  userRole      UserRole    @relation(fields: [userId, roleId], references: [userId, roleId])

  @@index(fields: [applicationId, userId, roleId])
}

model UserRole {
  userId            Int
  roleId            Int
  createdAt         DateTime            @default(now())
  updatedAt         DateTime            @updatedAt
  applications      Application[]
  creators          Conversation[]      @relation("creator")
  recipients        Conversation[]      @relation("recipient")
  jobs              Job[]
  authors           Message[]           @relation("author")
  notifications     Notification[]
  profile           Profile?
  role              Role                @relation(fields: [roleId], references: [roleId])
  user              User                @relation(fields: [userId], references: [userId])
  userLanguages     UserLanguage[]
  userCertification UserCertification[]
  picHistories      PicHistory[]

  @@id([userId, roleId])
}

model Profile {
  profileId               Int                      @id @default(autoincrement())
  userId                  Int
  status                  ProfileStatus            @default(ACCOUNT_CREATED)
  address                 String?
  birthDate               DateTime?
  companyId               Int?
  technicalSkillId        Int?
  countryId               Int?
  workPreferenceId        Int?                     @unique
  currentSalary           Float?
  email                   String[]
  expectedSalary          Float?
  firstName               String
  idCard                  String[]
  lastName                String
  fullName                String?
  preferredName           String?
  middleName              String?
  minSalary               Float?
  subcontractorRate       Float?
  monthlyRate             Float?
  hourlyRate              Float?
  phoneNumber             String?
  profilePhoto            String?
  socialLink              String[]
  cv                      String[]
  resumeSummary           String?
  roleId                  Int
  currencyId              Int?
  createdAt               DateTime                 @default(now())
  updatedAt               DateTime                 @updatedAt
  bypassSettingId         Int?
  isInCandidateCatalogue  Boolean?                 @default(false)
  seniority               Seniority?
  academy                 AcademyHistory[]
  employment              EmploymentHistory[]
  personalProject         PersonalProject[]
  award                   Award[]
  publication             Publication[]
  channel                 Channel[]
  userTechnicalSkills     UserTechnicalSkill[]
  company                 Company?                 @relation(fields: [companyId], references: [companyId])
  country                 Country?                 @relation(fields: [countryId], references: [countryId])
  currency                Currency?                @relation(fields: [currencyId], references: [currencyId])
  workPreference          WorkPreference?          @relation(fields: [workPreferenceId], references: [workPreferenceId])
  userRole                UserRole                 @relation(fields: [userId, roleId], references: [userId, roleId])
  userKeywords            UserKeyword[]
  CvTaskCandidate         CVTaskCandidate[]
  referralSource          ReferralSource?
  bypassSetting           CandidateBypassSettings? @relation(fields: [bypassSettingId], references: [id])
  profileManagedByAdminId Int?
  profileManagedByAdmin   ProfileManagedByAdmin?
  profileLogs             ProfileLog[]
  interviewRequests       InterviewRequest[]

  @@unique([userId, roleId])
  @@index([userId, roleId, bypassSettingId])
}

enum LogActionType {
  CREATE
  UPDATE
  DELETE
}

model ProfileLog {
  profileLogId    Int           @id @default(autoincrement())
  profileId       Int
  actionType      LogActionType
  changes         Json?
  changedAt       DateTime      @default(now())
  changeReason    String?
  isLatest        Boolean       @default(true)
  isSyncedToMongo Boolean       @default(false)
  profile         Profile       @relation(fields: [profileId], references: [profileId], onDelete: Cascade)

  @@index([profileId])
  @@index([changedAt])
  @@index([isLatest])
  @@index([isSyncedToMongo])
}

model InterviewRequest {
  id              Int                    @id @default(autoincrement())
  profileId       Int
  clientId        Int
  additionalNotes String?
  status          InterviewRequestStatus @default(PENDING)
  responseMessage String?
  createdAt       DateTime               @default(now())
  updatedAt       DateTime               @updatedAt

  profile Profile @relation(fields: [profileId], references: [profileId])
  client  User    @relation(fields: [clientId], references: [userId])
}

enum InterviewRequestStatus {
  PENDING
  APPROVED
  REJECTED
  SCHEDULED
  COMPLETED
  CANCELLED
}

model ProfileManagedByAdmin {
  profileManagedByAdminId Int      @id @default(autoincrement())
  profileId               Int      @unique
  createdAt               DateTime @default(now())
  updatedAt               DateTime @updatedAt

  topTechnicalSkills ProfileTopTechnicalSkill[]
  workingPositions   ProfileWorkingPosition[]
  profile            Profile                    @relation(fields: [profileId], references: [profileId])

  @@index([profileId])
}

model CandidateBypassSettings {
  id                 Int       @id @default(autoincrement())
  isSkipConversation Boolean   @default(false)
  isSkipLiveCoding   Boolean   @default(false)
  isSkipAiProctor    Boolean   @default(false)
  bypassReason       String?
  createdAt          DateTime  @default(now())
  updatedAt          DateTime  @updatedAt
  profile            Profile[]
}

model WorkPreference {
  workPreferenceId    Int               @id @default(autoincrement())
  countryId           Int?
  profileId           Int?              @unique
  workAvailability    WorkAvailability?
  hoursPerWeek        Int?
  currentSalary       Float?
  idealSalary         Float?
  minimumSalary       Float?
  startDateOption     WorkStartDay?
  startDate           DateTime?
  startAfterOfferDays Int?
  citizenship         String[]
  taxResidence        String?
  authorizedToWork    Boolean?
  currentlyWorking    Boolean?
  physicalLocation    UserCountry[]
  profile             Profile?
  createdAt           DateTime          @default(now())
  updatedAt           DateTime          @updatedAt
  employmentTypeId    Int?
  referralSource      ReferralSource?
}

model Job {
  jobId             Int                 @id @default(autoincrement())
  userId            Int
  roleId            Int
  businessId        Int?
  linkedId          Int?                @unique
  title             String
  description       String
  industryId        Int?
  location          String
  workingSchedule   String?
  startingSalary    Float
  endingSalary      Float
  currencySalaryId  Int
  postedDate        DateTime
  createdAt         DateTime            @default(now())
  updatedAt         DateTime            @updatedAt
  benefit           String
  requirement       String
  responsibility    String
  companyOverview   String?
  yearsExperience   String?
  jobActionReason   JobActionReason?    @relation(fields: [jobActionReasonId], references: [id])
  jobActionReasonId Int?                @unique
  requiredSkills    TechnicalSkill[]
  commitment        String?
  niceToHave        String?
  updatedBy         String?
  createdBy         String?
  jobDescriptionId  Int?
  isActive          Boolean?            @default(true)
  application       Application[]
  currency          Currency            @relation(fields: [currencySalaryId], references: [currencyId])
  industry          Industry?           @relation("industryId", fields: [industryId], references: [industryId])
  userRole          UserRole            @relation(fields: [userId, roleId], references: [userId, roleId])
  business          Business?           @relation(fields: [businessId], references: [businessId])
  jobEmployment     JobEmploymentType[]
  jobKeywords       JobKeyword[]
  jobLanguageSkill  JobLanguageSkill[]
  jobSkillLevel     JobSkill[]
  tasks             CVTask[]
  status            JobStatus?
  shortList         ShortList[]
  JobHistory        JobHistory[]
  interviewGroups   InterviewGroup[]
  jobLocationId     Int?
  jobLocation       JobLocation?        @relation(fields: [jobLocationId], references: [jobLocationId])
}

model JobDescription {
  id              Int      @id @default(autoincrement())
  createdAt       DateTime @default(now())
  createdBy       String?
  companyOverview String
  commitment      String
  level           String
  jobTitle        String
  yearsExperience String
  skillsRequired  String
  roleExpectation String
  mustHave        String
  niceToHave      String
  language        String   @default("N/A")
}

model JobHistory {
  id        String   @id @default(uuid())
  jobId     Int
  updatedBy String
  changes   Json
  updatedAt DateTime @default(now())

  job Job @relation(fields: [jobId], references: [jobId], onDelete: Cascade)
}

model JobActionReason {
  id               Int      @id @default(autoincrement())
  jobId            Int      @unique
  reasonToReopen   String?
  reasonToClose    String?
  createdAt        DateTime @default(now())
  updatedAt        DateTime @updatedAt

  Job Job[]
}

model ShortList {
  shortListId         Int                  @id @default(autoincrement())
  jobId               Int                  @unique
  status              ShortListStatus
  isActive            Boolean              @default(false)
  createdAt           DateTime             @default(now())
  updatedAt           DateTime             @updatedAt
  job                 Job                  @relation(fields: [jobId], references: [jobId])
  potentialCandidates PotentialCandidate[]
}

model PotentialCandidate {
  potentialCandidateId Int       @id @default(autoincrement())
  userId               Int
  shortListId          Int
  isActive             Boolean   @default(false)
  createdAt            DateTime  @default(now())
  updatedAt            DateTime  @updatedAt
  candidate            User      @relation(fields: [userId], references: [userId])
  shortList            ShortList @relation(fields: [shortListId], references: [shortListId])

  @@unique([shortListId, userId])
}

model Application {
  applicationId   Int                         @id @default(autoincrement())
  jobId           Int
  isActive        Boolean?                    @default(true)
  appliedDate     DateTime
  roleId          Int
  userId          Int
  createdAt       DateTime                    @default(now())
  updatedAt       DateTime                    @updatedAt
  status          ApplicationStatus
  rejectionReason String?
  internalStatus  InternalApplicationStatus[]
  remark          String?
  job             Job                         @relation(fields: [jobId], references: [jobId])
  userRole        UserRole                    @relation(fields: [userId, roleId], references: [userId, roleId])
  foundationTest  FoundationTest?
  interview       Interview[]
  picHistories    PicHistory[]

  @@unique([jobId, userId, roleId])
}

model MasterForm {
  masterFormEmail      String                 @id
  createdAt            DateTime               @default(now())
  updatedAt            DateTime               @updatedAt
  status               MasterFormStatus
  resume               ResumeUploadSection?
  skillSelection       SkillSelectionSection?
  aiBotTest            AIBotTestSection?
  codingTest           CodingTestSection?
  englishTest          EnglishTestSection?
  workPreference       WorkPreferenceSection?
}

model ResumeUploadSection {
  resumeUploadSectionId Int              @id @default(autoincrement())
  masterFormEmail       String?          @unique
  resumeUrl             String
  resumeContent         Json?
  status                MasterFormStatus
  createdAt             DateTime         @default(now())
  updatedAt             DateTime         @updatedAt
  masterForm            MasterForm?      @relation(fields: [masterFormEmail], references: [masterFormEmail])
}

model SkillInterviewSelected {
  skillInterviewSelectedId Int                    @id @default(autoincrement())
  skillSelectionSectionId  Int
  technicalSkillId         Int
  skillLevelId             Int
  createdAt                DateTime               @default(now())
  updatedAt                DateTime               @updatedAt
  skillLevel               SkillLevel?            @relation(fields: [skillLevelId], references: [skillLevelId])
  technicalSkill           TechnicalSkill?        @relation(fields: [technicalSkillId], references: [technicalSkillId])
  skillSelectionSection    SkillSelectionSection? @relation(fields: [skillSelectionSectionId], references: [skillSelectionSectionId])
}

model SkillSelectionSection {
  skillSelectionSectionId Int                      @id @default(autoincrement())
  masterFormEmail         String?                  @unique
  skillInterviewSelected  SkillInterviewSelected[]
  status                  MasterFormStatus
  createdAt               DateTime                 @default(now())
  updatedAt               DateTime                 @updatedAt
  masterForm              MasterForm?              @relation(fields: [masterFormEmail], references: [masterFormEmail])
}

model CodingTestSection {
  codingTestSectionId Int              @id @default(autoincrement())
  masterFormEmail     String?          @unique
  createdAt           DateTime         @default(now())
  updatedAt           DateTime         @updatedAt
  numberOfTrial       Int              @default(0)
  score               Int?
  screenRecordUrl     String?
  webcamRecordUrl     String?
  status              TestStatus
  question            CodingQuestion[]
  masterForm          MasterForm?      @relation(fields: [masterFormEmail], references: [masterFormEmail])
  roomAccesses        RoomAccess[]
}

model AIBotTestSection {
  aiBotTestSectionId   Int                 @id @default(autoincrement())
  masterFormEmail      String?             @unique
  createdAt            DateTime            @default(now())
  updatedAt            DateTime            @updatedAt
  score                Int?
  status               TestStatus
  numberOfTrial        Int                 @default(0)
  conversationId       String?
  conversationName     String?
  screenRecordUrl      String?
  audioRecordUrl       String?
  question             AIBotTestQuestion[]
  mlConversationResult Json?
  masterForm           MasterForm?         @relation(fields: [masterFormEmail], references: [masterFormEmail])
  aIBotTestRooms       AIBotTestRoom[]
}

model EnglishTestSection {
  englishTestSectionId Int         @id @default(autoincrement())
  masterFormEmail      String?     @unique
  question             String
  userAnswer           String
  createdAt            DateTime    @default(now())
  updatedAt            DateTime    @updatedAt
  score                Int
  conversationId       String?
  mlScoringResult      Json?
  status               TestStatus
  screenRecordUrl      String?
  webcamRecordUrl      String?
  masterForm           MasterForm? @relation(fields: [masterFormEmail], references: [masterFormEmail])
}

model WorkPreferenceSection {
  workPreferenceSectionId Int              @id @default(autoincrement())
  masterFormEmail         String?          @unique
  workPreferenceContent   Json?
  status                  MasterFormStatus
  createdAt               DateTime         @default(now())
  updatedAt               DateTime         @updatedAt
  masterForm              MasterForm?      @relation(fields: [masterFormEmail], references: [masterFormEmail])
}

model FoundationTest {
  foundationTestId Int            @id @default(autoincrement())
  applicationId    Int            @unique
  createdAt        DateTime       @default(now())
  updatedAt        DateTime       @updatedAt
  status           TestStatus
  aiBotTest        AIBotTest?
  codingTest       CodingTest?
  englishTest      EnglishTest?
  application      Application    @relation(fields: [applicationId], references: [applicationId])
  technicalTest    TechnicalTest?
}

model EnglishTest {
  englishTestId    Int            @id @default(autoincrement())
  foundationTestId Int            @unique
  question         String
  userAnswer       String
  createdAt        DateTime       @default(now())
  updatedAt        DateTime       @updatedAt
  score            Int
  status           TestStatus
  screenRecordUrl  String?
  webcamRecordUrl  String?
  foundationTest   FoundationTest @relation(fields: [foundationTestId], references: [foundationTestId])
}

model TechnicalTest {
  technicalTestId  Int                     @id @default(autoincrement())
  foundationTestId Int                     @unique
  createdAt        DateTime                @default(now())
  updatedAt        DateTime                @updatedAt
  score            Int
  status           TestStatus
  screenRecordUrl  String?
  webcamRecordUrl  String?
  foundationTest   FoundationTest          @relation(fields: [foundationTestId], references: [foundationTestId])
  question         TechnicalTestQuestion[]
}

model TechnicalTestQuestion {
  technicalTestQuestionId Int               @id @default(autoincrement())
  interviewQuestionId     Int
  technicalTestId         Int?
  answer                  String
  score                   Int
  createdAt               DateTime          @default(now())
  updatedAt               DateTime          @updatedAt
  question                InterviewQuestion @relation(fields: [interviewQuestionId], references: [interviewQuestionId])
  technicalTest           TechnicalTest?    @relation(fields: [technicalTestId], references: [technicalTestId])
}

model AIBotTestQuestion {
  aiBotTestQuestionId  Int      @id @default(autoincrement())
  aiBotTestId          Int?
  aiBotTestSectionId   Int?
  messageId            String?
  audioRecordUrl       String?
  question             String?
  answer               String?
  messageSender        String?
  messageSenderContent String?
  score                Int
  createdAt            DateTime @default(now())
  updatedAt            DateTime @updatedAt

  AIBotTest        AIBotTest?        @relation(fields: [aiBotTestId], references: [aiBotTestId])
  aiBotTestSection AIBotTestSection? @relation(fields: [aiBotTestSectionId], references: [aiBotTestSectionId])
}

model CodingQuestion {
  codingQuestionId    Int                @id @default(autoincrement())
  interviewQuestionId Int
  codingTestId        Int?
  codingTestSectionId Int?
  answer              String
  score               Int?
  compileResult       String?
  createdAt           DateTime           @default(now())
  updatedAt           DateTime           @updatedAt
  compileResultId     Int?
  codingLanguageId    Int?
  codingLanguage      String?
  codingTest          CodingTest?        @relation(fields: [codingTestId], references: [codingTestId])
  codingTestSection   CodingTestSection? @relation(fields: [codingTestSectionId], references: [codingTestSectionId])
  question            InterviewQuestion  @relation(fields: [interviewQuestionId], references: [interviewQuestionId])
}

model CodingTest {
  codingTestId     Int              @id @default(autoincrement())
  foundationTestId Int?             @unique
  createdAt        DateTime         @default(now())
  updatedAt        DateTime         @updatedAt
  score            Int
  screenRecordUrl  String?
  webcamRecordUrl  String?
  status           TestStatus
  question         CodingQuestion[]
  foundationTest   FoundationTest?  @relation(fields: [foundationTestId], references: [foundationTestId])
}

model AIBotTest {
  aiBotTestId      Int                 @id @default(autoincrement())
  foundationTestId Int?                @unique
  createdAt        DateTime            @default(now())
  updatedAt        DateTime            @updatedAt
  conversationId   String?
  screenRecordUrl  String?
  audioRecordUrl   String?
  numberOfTrial    Int                 @default(0)
  score            Int?
  status           TestStatus?
  foundationTest   FoundationTest?     @relation(fields: [foundationTestId], references: [foundationTestId])
  question         AIBotTestQuestion[]
  aIBotTestRooms   AIBotTestRoom[]
}

model AIBotTestRoom {
  aIBotTestRoomId    Int                  @id @default(autoincrement())
  aiBotTestSectionId Int?
  interviewCandidateId String?                @unique
  aiBotTestId        Int?
  roomName           String?              @unique
  roomUrl            String?
  roomNumber         Int?
  roomExpireTime     DateTime?
  roomStatus         AIBotTestRoomStatus?
  roomToken          String?
  duration           Int?
  recordingId        String?
  recordingUrl       String?
  recordingStatus    String?
  createdAt          DateTime             @default(now())
  updatedAt          DateTime             @updatedAt
  aiBotTest          AIBotTest?           @relation(fields: [aiBotTestId], references: [aiBotTestId])
  aiBotTestSection   AIBotTestSection?    @relation(fields: [aiBotTestSectionId], references: [aiBotTestSectionId])
  interviewCandidate InterviewCandidate? @relation(fields: [interviewCandidateId], references: [interviewCandidateId], onDelete: Cascade)
  meetingTranscription MeetingTranscription[]
  meetingScoring     MeetingScoring?     @relation("RoomScoring")
  handleScoringStatus HandleScoringStatus? @default(PENDING)
}

model Interview {
  interviewId   Int         @id @default(autoincrement())
  applicationId Int
  schedule      DateTime
  format        String
  createdAt     DateTime    @default(now())
  updatedAt     DateTime    @updatedAt
  application   Application @relation(fields: [applicationId], references: [applicationId])
}

model Message {
  messageId      Int          @id @default(autoincrement())
  content        String
  createdAt      DateTime     @default(now())
  updatedAt      DateTime     @updatedAt
  authorId       Int
  authorRoleId   Int
  conversationId Int
  author         UserRole     @relation("author", fields: [authorId, authorRoleId], references: [userId, roleId])
  conversation   Conversation @relation(fields: [conversationId], references: [conversationId])
}

model Conversation {
  conversationId  Int       @id @default(autoincrement())
  creatorId       Int
  recipientId     Int
  creatorRoleId   Int
  recipientRoleId Int
  createdAt       DateTime  @default(now())
  updatedAt       DateTime  @updatedAt
  creator         UserRole  @relation("creator", fields: [creatorId, creatorRoleId], references: [userId, roleId])
  recipient       UserRole  @relation("recipient", fields: [recipientId, recipientRoleId], references: [userId, roleId])
  message         Message[]
}

model Notification {
  notificationId Int      @id @default(autoincrement())
  userId         Int
  content        String
  roleId         Int
  createdAt      DateTime @default(now())
  updatedAt      DateTime @updatedAt
  userRole       UserRole @relation(fields: [userId, roleId], references: [userId, roleId])
}

model Report {
  reportId      Int      @id @default(autoincrement())
  recruiterId   Int
  type          String
  generatedDate DateTime
  createdAt     DateTime @default(now())
  updatedAt     DateTime @updatedAt
  user          User     @relation(fields: [recruiterId], references: [userId])
}

model Industry {
  industryId Int        @id @default(autoincrement())
  name       String
  isActive   Boolean?   @default(true)
  createdAt  DateTime   @default(now())
  updatedAt  DateTime   @updatedAt
  company    Company[]
  job        Job[]      @relation("industryId")
  Business   Business[]
}

model SkillLevel {
  skillLevelId           Int                      @id @default(autoincrement())
  name                   String
  isActive               Boolean?                 @default(true)
  createdAt              DateTime                 @default(now())
  updatedAt              DateTime                 @updatedAt
  jobSkill               JobSkill[]
  skillInterviewSelected SkillInterviewSelected[]
}

model JobSkill {
  jobId        Int
  skillLevelId Int
  createdAt    DateTime   @default(now())
  updatedAt    DateTime   @updatedAt
  job          Job        @relation(fields: [jobId], references: [jobId])
  skillLevel   SkillLevel @relation(fields: [skillLevelId], references: [skillLevelId])

  @@id([jobId, skillLevelId])
}

model EmploymentType {
  id               Int                      @id @default(autoincrement())
  name             String
  isActive         Boolean?                 @default(true)
  createdAt        DateTime                 @default(now())
  updatedAt        DateTime                 @updatedAt
  jobEmployment    JobEmploymentType[]
  workingPositions WorkingPositionHistory[]
}

model JobEmploymentType {
  id           Int            @id @default(autoincrement())
  jobId        Int
  employmentId Int
  createdAt    DateTime       @default(now())
  updatedAt    DateTime       @updatedAt
  employment   EmploymentType @relation(fields: [employmentId], references: [id])
  job          Job            @relation(fields: [jobId], references: [jobId])
}

model Currency {
  currencyId                  Int                           @id @default(autoincrement())
  code                        String                        @unique
  name                        String
  flagImage                   String?
  createdAt                   DateTime                      @default(now())
  updatedAt                   DateTime                      @updatedAt
  AffiliateReward             AffiliateReward[]
  AffiliateRewardTransaction  AffiliateRewardTransaction[]
  CouponCode                  CouponCode[]
  CouponRedemptionTransaction CouponRedemptionTransaction[]
  Job                         Job[]
  Profile                     Profile[]
  Business                    Business[]
}

model JobLanguageSkill {
  languageId  Int
  jobId       Int
  proficiency Proficiency
  createdAt   DateTime    @default(now())
  updatedAt   DateTime    @updatedAt
  job         Job         @relation(fields: [jobId], references: [jobId])
  language    Language    @relation(fields: [languageId], references: [languageId])

  @@id([jobId, languageId])
}

model AffiliateReward {
  affiliateRewardId          Int                         @id @default(autoincrement())
  referrerCompanyId          Int
  referredCompanyId          Int
  currencyId                 Int
  percentage                 Decimal
  createdAt                  DateTime                    @default(now())
  updatedAt                  DateTime                    @updatedAt
  currency                   Currency                    @relation(fields: [currencyId], references: [currencyId])
  referredCompany            Company                     @relation("referredCompanyId", fields: [referredCompanyId], references: [companyId])
  referrerCompany            Company                     @relation("referrerCompanyId", fields: [referrerCompanyId], references: [companyId])
  AffiliateRewardTransaction AffiliateRewardTransaction?
}

model CouponCode {
  id                          Int                          @id @default(autoincrement())
  companyId                   Int
  code                        String                       @unique
  discountAmount              Decimal
  currencyId                  Int
  validFrom                   DateTime
  validTo                     DateTime
  usageDurationDays           Int
  createdAt                   DateTime                     @default(now())
  updatedAt                   DateTime                     @updatedAt
  company                     Company                      @relation(fields: [companyId], references: [companyId])
  currency                    Currency                     @relation(fields: [currencyId], references: [currencyId])
  CouponRedemptionTransaction CouponRedemptionTransaction?
}

model AffiliateRewardTransaction {
  id         Int             @id @default(autoincrement())
  rewardId   Int             @unique
  amount     Decimal
  currencyId Int
  date       DateTime
  createdAt  DateTime        @default(now())
  updatedAt  DateTime        @updatedAt
  currency   Currency        @relation(fields: [currencyId], references: [currencyId])
  reward     AffiliateReward @relation(fields: [rewardId], references: [affiliateRewardId])
}

model CouponRedemptionTransaction {
  id           Int        @id @default(autoincrement())
  couponCodeId Int        @unique
  amount       Decimal
  currencyId   Int
  date         DateTime
  createdAt    DateTime   @default(now())
  updatedAt    DateTime   @updatedAt
  couponCode   CouponCode @relation(fields: [couponCodeId], references: [id])
  currency     Currency   @relation(fields: [currencyId], references: [currencyId])
}

model Keyword {
  content      String
  linkedId     Int?          @unique
  isActive     Boolean?      @default(true)
  createdAt    DateTime      @default(now())
  updatedAt    DateTime      @updatedAt
  keywordId    Int           @id @default(autoincrement())
  jobKeywords  JobKeyword[]
  userKeywords UserKeyword[]
}

model UserKeyword {
  profileId Int
  keywordId Int
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  keyword   Keyword  @relation(fields: [keywordId], references: [keywordId], onDelete: Cascade)
  profile   Profile  @relation(fields: [profileId], references: [profileId], onDelete: Cascade)

  @@id([profileId, keywordId])
}

model JobKeyword {
  jobId     Int
  keywordId Int
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  job       Job      @relation(fields: [jobId], references: [jobId], onDelete: Cascade)
  keyword   Keyword  @relation(fields: [keywordId], references: [keywordId], onDelete: Cascade)

  @@id([jobId, keywordId])
}

model TypeDegree {
  typeDegreeId Int              @id @default(autoincrement())
  name         String
  createdAt    DateTime         @default(now())
  updatedAt    DateTime         @updatedAt
  academy      AcademyHistory[]
}

model LanguageProficiency {
  languageProficiencyId   Int            @id @default(autoincrement())
  languageProficiencyName String
  createdAt               DateTime       @default(now())
  updatedAt               DateTime       @updatedAt
  userLanguage            UserLanguage[]
}

model DegreeStatus {
  degreeStatusId   Int              @id @default(autoincrement())
  degreeStatusName String
  createdAt        DateTime         @default(now())
  updatedAt        DateTime         @updatedAt
  academy          AcademyHistory[]
}

model AcademyHistory {
  profileId         Int
  universityName    String?
  major             String?
  gpa               Float?
  gpaNumerator      Float?
  gpaDenominator    Float?
  startDate         DateTime?
  endDate           DateTime?
  currentlyPursuing Boolean?
  degreeId          Int?
  degreeStatusId    Int?
  proof             String[]
  createdAt         DateTime      @default(now())
  updatedAt         DateTime      @updatedAt
  academyHistoryId  Int           @id @default(autoincrement())
  degree            TypeDegree?   @relation(fields: [degreeId], references: [typeDegreeId])
  degreeStatus      DegreeStatus? @relation(fields: [degreeStatusId], references: [degreeStatusId])
  profile           Profile       @relation(fields: [profileId], references: [profileId], onDelete: Cascade)
}

model EmploymentTechnicalSkill {
  employmentTechnicalSkillId Int               @id @default(autoincrement())
  employmentHistoryId        Int
  technicalSkillId           Int?
  createdAt                  DateTime          @default(now())
  updatedAt                  DateTime          @updatedAt
  technicalSkill             TechnicalSkill?   @relation(fields: [technicalSkillId], references: [technicalSkillId])
  employmentHistory          EmploymentHistory @relation(fields: [employmentHistoryId], references: [employmentHistoryId])
}

model EmploymentHistory {
  profileId                Int
  companyName              String?
  startDate                DateTime?
  endDate                  DateTime?
  description              String[]
  commitment               String?
  currentlyWorking         Boolean?
  countryId                Int?
  country                  Country?                   @relation(fields: [countryId], references: [countryId])
  cityId                   Int?
  city                     City?                      @relation(fields: [cityId], references: [cityId])
  employmentTechnicalSkill EmploymentTechnicalSkill[]
  createdAt                DateTime                   @default(now())
  updatedAt                DateTime                   @updatedAt
  employmentHistoryId      Int                        @id @default(autoincrement())
  profile                  Profile                    @relation(fields: [profileId], references: [profileId], onDelete: Cascade)
  workingPositionHistory   WorkingPositionHistory[]
}

model PersonalProject {
  personalProjectId Int       @id @default(autoincrement())
  profileId         Int
  projectName       String?
  startDate         DateTime?
  endDate           DateTime?
  currentlyDoing    Boolean?
  workingPosition   String?
  description       String[]
  createdAt         DateTime  @default(now())
  updatedAt         DateTime  @updatedAt
  profile           Profile   @relation(fields: [profileId], references: [profileId], onDelete: Cascade)
}

model Publication {
  id                Int       @id @default(autoincrement())
  profileId         Int
  title             String?
  publicationName   String?
  dateOfPublication DateTime?
  link              String?
  authors           String[]
  description       String?
  profile           Profile   @relation(fields: [profileId], references: [profileId], onDelete: Cascade)
  createdAt         DateTime  @default(now())
  updatedAt         DateTime  @updatedAt
}

model Award {
  id        Int       @id @default(autoincrement())
  profileId Int
  title     String?
  date      DateTime?
  profile   Profile   @relation(fields: [profileId], references: [profileId], onDelete: Cascade)
  createdAt DateTime  @default(now())
  updatedAt DateTime  @updatedAt
}

model Channel {
  channelId   Int          @id @default(autoincrement())
  profileId   Int?
  type        ChannelType?
  platformId  Int?
  platformUrl String?
  createdAt   DateTime     @default(now())
  updatedAt   DateTime     @updatedAt

  profile  Profile?         @relation(fields: [profileId], references: [profileId], onDelete: Cascade)
  platform PlatformChannel? @relation(fields: [platformId], references: [platformId])
}

model PlatformChannel {
  platformId   Int          @id @default(autoincrement())
  platFormName String       @unique
  logoUrl      String?
  description  String?
  type         ChannelType?
  createdAt    DateTime     @default(now())
  updatedAt    DateTime     @updatedAt

  channel Channel[]
}

model UserTechnicalSkill {
  userTechnicalSkillId Int             @id @default(autoincrement())
  profileId            Int
  technicalSkillId     Int?
  createdAt            DateTime        @default(now())
  updatedAt            DateTime        @updatedAt
  technicalSkill       TechnicalSkill? @relation(fields: [technicalSkillId], references: [technicalSkillId])
  profile              Profile         @relation(fields: [profileId], references: [profileId])
}

model ProfileTopTechnicalSkill {
  profileManagedByAdminId Int
  technicalSkillId        Int
  createdAt               DateTime              @default(now())
  updatedAt               DateTime              @updatedAt
  technicalSkill          TechnicalSkill        @relation(fields: [technicalSkillId], references: [technicalSkillId])
  profileManagedByAdmin   ProfileManagedByAdmin @relation(fields: [profileManagedByAdminId], references: [profileManagedByAdminId])

  @@id([profileManagedByAdminId, technicalSkillId])
  @@index([profileManagedByAdminId])
}

model TechnicalSkill {
  technicalSkillId         Int                        @id @default(autoincrement())
  skillName                String
  linkedId                 Int?                       @unique
  createdAt                DateTime                   @default(now())
  updatedAt                DateTime                   @updatedAt
  userTechnicalSkill       UserTechnicalSkill[]
  employmentTechnicalSkill EmploymentTechnicalSkill[]
  skillInterviewSelected   SkillInterviewSelected[]
  Job                      Job[]
  UserTopTechnicalSkill    ProfileTopTechnicalSkill[]

  InterviewCandidate InterviewCandidate[]
}

model WorkingPositionCategory {
  categoryId       Int               @id @default(autoincrement())
  name             String            @unique
  description      String?
  createdAt        DateTime          @default(now())
  updatedAt        DateTime          @updatedAt
  workingPositions WorkingPosition[]
}

model WorkingPosition {
  workingPositionId   Int                      @id @default(autoincrement())
  position            String
  description         String?
  createdAt           DateTime                 @default(now())
  updatedAt           DateTime                 @updatedAt
  categoryId          Int
  category            WorkingPositionCategory  @relation(fields: [categoryId], references: [categoryId])
  UserWorkingPosition ProfileWorkingPosition[]

  @@unique([position, categoryId])
  @@index([categoryId])
}

model ProfileWorkingPosition {
  profileManagedByAdminId Int
  workingPositionId       Int
  createdAt               DateTime              @default(now())
  updatedAt               DateTime              @updatedAt
  workingPosition         WorkingPosition       @relation(fields: [workingPositionId], references: [workingPositionId])
  profileManagedByAdmin   ProfileManagedByAdmin @relation(fields: [profileManagedByAdminId], references: [profileManagedByAdminId])

  @@id([profileManagedByAdminId, workingPositionId])
  @@index([profileManagedByAdminId])
}

model WorkingPositionHistory {
  employmentHistoryId      Int
  position                 String
  positionStartDate        DateTime?
  positionEndDate          DateTime?
  createdAt                DateTime          @default(now())
  updatedAt                DateTime          @updatedAt
  workingPositionHistoryId Int               @id @default(autoincrement())
  skills                   String?
  employmentHistory        EmploymentHistory @relation(fields: [employmentHistoryId], references: [employmentHistoryId], onDelete: Cascade)
  employmentType           EmploymentType?   @relation(fields: [employmentTypeId], references: [id])
  employmentTypeId         Int?
}

model InterviewQuestion {
  question              String
  answer                String
  level                 String
  skill                 String
  category              String
  source                String
  createdAt             DateTime?               @default(now())
  updatedAt             DateTime?               @updatedAt
  interviewQuestionId   Int                     @id @default(autoincrement())
  testCases             String?
  codeTemplate          String?
  codeTestCases         String?
  CodeTemplate          CodeTemplate[]
  codingQuestion        CodingQuestion[]
  technicalTestQuestion TechnicalTestQuestion[]
}

model CVTask {
  taskId            Int               @id @default(autoincrement())
  jobId             Int?
  title             String?
  description       String?
  status            TaskStatus
  jobDetailScanning Json?
  scanAllCandidates Boolean           @default(false)
  createdAt         DateTime          @default(now())
  updatedAt         DateTime          @updatedAt
  job               Job?              @relation(fields: [jobId], references: [jobId])
  candidates        CVTaskCandidate[]

  @@index([jobId])
}

model CVTaskCandidate {
  taskId                  Int
  profileId               Int
  score                   Int?
  isActive                Boolean? @default(true)
  candidateDetailScanning Json?
  createdAt               DateTime @default(now())
  updatedAt               DateTime @updatedAt
  CVTask                  CVTask   @relation(fields: [taskId], references: [taskId])
  Profile                 Profile  @relation(fields: [profileId], references: [profileId])

  @@id([taskId, profileId])
  @@index([taskId])
}

model CodeTemplate {
  codeTemplateId      Int                @id @default(autoincrement())
  interviewQuestionId Int?
  codingLanguageId    Int?
  codingLanguage      String?
  testCases           String?
  codeTemplate        String?
  codeTestCases       String?
  testCaseTemplate    String?
  createdAt           DateTime           @default(now())
  updatedAt           DateTime           @updatedAt
  interviewQuestion   InterviewQuestion? @relation(fields: [interviewQuestionId], references: [interviewQuestionId])
}

model BusyTime {
  busyTimeId    Int         @id @default(autoincrement())
  meetingUserId Int
  startTime     DateTime
  endTime       DateTime
  isActive      Boolean?    @default(true)
  createdAt     DateTime    @default(now())
  updatedAt     DateTime    @updatedAt
  MeetingUser   MeetingUser @relation(fields: [meetingUserId], references: [meetingUserId])
}

model Meeting {
  createdAt                                   DateTime      @default(now())
  updatedAt                                   DateTime      @updatedAt
  customerId                                  Int?
  endTime                                     DateTime
  hostId                                      Int
  linkMeeting                                 String?
  meetingId                                   Int           @id @default(autoincrement())
  calendarId                                  String?
  calendarEventId                             String?
  startTime                                   DateTime
  timezone                                    String?
  reasonCancel                                String?
  isBufferTime                                Boolean?      @default(false)
  status                                      MeetingStatus
  MeetingUser_Meeting_customerIdToMeetingUser MeetingUser?  @relation("Meeting_customerIdToMeetingUser", fields: [customerId], references: [meetingUserId])
  MeetingUser_Meeting_hostIdToMeetingUser     MeetingUser   @relation("Meeting_hostIdToMeetingUser", fields: [hostId], references: [meetingUserId])
}

model MeetingUser {
  meetingUserId                           Int        @id @default(autoincrement())
  firstName                               String
  lastName                                String
  workEmail                               String
  priority                                Int?
  timezone                                String?
  companySize                             String?
  createdAt                               DateTime   @default(now())
  updatedAt                               DateTime   @updatedAt
  isHost                                  Boolean
  headquarterId                           Int?
  stateHeadquarter                        String?
  phoneNumber                             String?
  website                                 String?
  BusyTime                                BusyTime[]
  Meeting_Meeting_customerIdToMeetingUser Meeting[]  @relation("Meeting_customerIdToMeetingUser")
  Meeting_Meeting_hostIdToMeetingUser     Meeting[]  @relation("Meeting_hostIdToMeetingUser")
  Headquarter                             Country?   @relation(fields: [headquarterId], references: [countryId])
}

model ContactSupport {
  contactSupportId Int                  @id @default(autoincrement())
  fullName         String
  phoneNumber      String?
  email            String?
  message          String?
  proof            String?
  status           ContactSupportStatus
  createdAt        DateTime             @default(now())
  updatedAt        DateTime             @updatedAt
}

model VerificationCode {
  verificationCodeId Int      @id @default(autoincrement())
  code               String   @unique
  type               String?
  email              String?
  isUsed             Boolean  @default(false)
  expiredAt          DateTime
  createdAt          DateTime @default(now())
  updatedAt          DateTime @updatedAt
}

model Business {
  businessId         Int               @id @default(autoincrement())
  email              String
  isVerified         Boolean?          @default(false)
  linkedId           Int?              @unique
  verficationToken   String?
  firstName          String?
  middleName         String?
  lastName           String?
  preferredFirstName String?
  countryCode        String?
  phoneNumber        String?
  jobTitleId         Int?
  description        String?
  discoverySourceId  Int?
  organizationName   String?
  organizationSizeId Int?
  headquarterId      Int?
  industryId         Int?
  stateHeadquarterId Int?
  startWorkingTime   String?
  endWorkingTime     String?
  timezone           String?
  currencyId         Int?
  departmentId       Int?
  companyWebsiteURL  String?
  companyLinkedInURL String?
  userId             Int?              @unique
  createdAt          DateTime          @default(now())
  updatedAt          DateTime          @updatedAt
  User               User?             @relation(fields: [userId], references: [userId])
  Jobs               Job[]
  DiscoverySource    DiscoverySource?  @relation(fields: [discoverySourceId], references: [discoverySourceId])
  Industry           Industry?         @relation(fields: [industryId], references: [industryId])
  Headquarter        Country?          @relation(fields: [headquarterId], references: [countryId])
  StateHeadquarter   StateHeadquarter? @relation(fields: [stateHeadquarterId], references: [stateHeadquarterId])
  OrganizationSize   OrganizationSize? @relation(fields: [organizationSizeId], references: [organizationSizeId])
  Department         Department?       @relation(fields: [departmentId], references: [departmentId])
  Currency           Currency?         @relation(fields: [currencyId], references: [currencyId])
  JobTitle           JobTitle?         @relation(fields: [jobTitleId], references: [jobTitleId])
}

model JobCategory {
  id        Int        @id @default(autoincrement())
  name      String     @unique // e.g., "Human Resources", "Finance"
  order     Int? // For display order
  isActive  Boolean    @default(true)
  createdAt DateTime   @default(now())
  updatedAt DateTime   @updatedAt
  jobTitles JobTitle[]
}

model JobTitle {
  jobTitleId Int         @id @default(autoincrement())
  name       String // e.g., "HR Generalist", "Payroll Manager"
  order      Int? // For display order within category
  isCustom   Boolean     @default(false) // For "Other" titles
  isActive   Boolean     @default(true)
  categoryId Int
  createdAt  DateTime    @default(now())
  updatedAt  DateTime    @updatedAt
  category   JobCategory @relation(fields: [categoryId], references: [id])
  businesses Business[]
}

model Department {
  departmentId Int        @id @default(autoincrement())
  name         String
  createdAt    DateTime   @default(now())
  updatedAt    DateTime   @updatedAt
  business     Business[]
}

model DiscoverySource {
  discoverySourceId Int        @id @default(autoincrement())
  name              String     @unique
  description       String?
  isCustom          Boolean    @default(false) // To identify if it's a user-added source
  isActive          Boolean    @default(true)
  createdAt         DateTime   @default(now())
  updatedAt         DateTime   @updatedAt
  Businesses        Business[]
}

model StateHeadquarter {
  stateHeadquarterId Int        @id @default(autoincrement())
  name               String
  countryId          Int
  createdAt          DateTime   @default(now())
  updatedAt          DateTime   @updatedAt
  Country            Country    @relation(fields: [countryId], references: [countryId])
  Business           Business[]
}

model OrganizationSize {
  organizationSizeId Int        @id @default(autoincrement())
  minSize            Int // Lower bound
  maxSize            Int? // Upper bound (null for 500+)
  label              String // e.g., "1 - 4", "500+"
  order              Int? // To maintain specific display order
  isActive           Boolean    @default(true)
  businesses         Business[]
}

model MagicToken {
  magicTokenId Int       @id @default(autoincrement())
  userId       Int
  token        String    @unique
  expiresAt    DateTime?
  jwtExpiresAt DateTime?
  isUsed       Boolean   @default(false)
  deviceId     String?
  isActive     Boolean   @default(true)
  createdAt    DateTime  @default(now())
  updatedAt    DateTime  @updatedAt
  User         User      @relation(fields: [userId], references: [userId])

  @@index([userId, token])
}

enum JobDescriptionStatus {
  CLOSED
  DRAFT
  UNDER_REVIEW
  OPEN
}

model TokenUsageLog {
  id                Int      @id @default(autoincrement())
  taskId            Int
  jobId             Int
  profileId         Int
  inputTokens       Int
  outputTokens      Int
  requestType       String
  inputTokenDetail  Json?
  outputTokenDetail Json?
  createdAt         DateTime @default(now())
  updatedAt         DateTime @updatedAt
}

enum Proficiency {
  Beginner
  Intermediate
  Advanced
  Native
}

enum ApplicationStatus {
  APPLICATION_PENDING
  APPLICATION_REJECTED
  APPLICATION_APPROVED
}

enum ProfileStatus {
  ACCOUNT_CREATED
  RESUME_SUBMITTED
  AI_INTERVIEW_COMPLETED
  PASSED_AI_INTERVIEW
  FAILED_AI_INTERVIEW
  WORK_PREFERENCE_SPECIFIED
  HUMAN_INTERVIEW_COMPLETED
  PASSED_HUMAN_INTERVIEW
  FAILED_HUMAN_INTERVIEW
  PROFILE_COMPLETED
  CERTIFIED
}

enum TestStatus {
  TEST_PENDING
  TEST_COMPLETED
  TEST_PASSED
  TEST_FAILED
  TEST_NOTIFIED
  GREY_ZONE
}

enum CompanySize {
  SMALL
  MEDIUM
  LARGE
  ENTERPRISE
}

enum MeetingStatus {
  SCHEDULING
  SUCCESSFULLY_SCHEDULED
  FAILED_TO_SCHEDULE
  CANCELLED
}

enum TaskStatus {
  SCHEDULED
  RUNNING
  CANCELLED
  COMPLETE
  ERROR
}

enum JobStatus {
  OPEN
  CLOSED
}

enum InternalApplicationStatus {
  CV_SCANNING
  PHONE_INTERVIEW
  AI_TEST
  HOME_ASSIGNMENT
  ONLINE_ASSESSMENT
  HUMAN_INTERVIEW
  WITHDREW_B4_SL
  FAILED
  CONTRACTING
  WITHDREW
  SENT_TO_CLIENT
  CONTRACT_SIGNED
  DECLINED
  INTERVIEW_DONE
  INITIATING_APPLICATION_PROCESS
}

enum ShortListStatus {
  DRAFTED
  PUBLISHED
}

enum ReferralSource {
  Facebook
  Linkedin
  Google
  Other
}

enum MasterFormStatus {
  PENDING
  COMPLETED
}

enum WorkAvailability {
  FullTime
  HalfTime
  Flexible
}

enum WorkStartDay {
  Immediately
  AfterSpecificDate
  AfterOffer
}

enum ChannelType {
  SOCIAL_MEDIA
  CODING
  PORTFOLIO
}

enum ContactSupportStatus {
  PENDING
  NOTIFIED
}

enum Seniority {
  INTERN
  NEW_GRAD
  JUNIOR
  MID
  SENIOR
}

enum AIBotTestRecordingStatus {
  RECORDING
  COMPLETED
}

enum AIBotTestRoomStatus {
  ROOM_CREATED
  SCAN_COMPLETED
  INTERVIEW_COMPLETED
}

enum InterviewGroupStatus {
  OPEN
  CLOSED
}

model InterviewGroup {
  interviewGroupId   String               @id @default(uuid())
  jobId              Int
  name               String
  status             InterviewGroupStatus @default(OPEN)
  totalDuration      Int?
  createdAt          DateTime             @default(now())
  updatedAt          DateTime             @updatedAt
  job                Job                  @relation(fields: [jobId], references: [jobId], onDelete: Cascade)
  InterviewCandidate InterviewCandidate[]
  customQuestions    CustomQuestion[]

  @@unique([jobId, name])
  @@index([jobId])
}

model InterviewCandidate {
  interviewCandidateId String                   @id @default(uuid())
  interviewGroupId     String
  profileId            Int?
  name                 String
  avatar               String?
  yearOfExperience     Int?
  status               InterviewCandidateStatus @default(PROCESSING)
  summary              String?
  favorite             Boolean?                 @default(false)
  technicalSkills      TechnicalSkill[]
  interviewCompletedAt DateTime?
  createdAt            DateTime                 @default(now())
  updatedAt            DateTime                 @updatedAt
  interviewGroup       InterviewGroup           @relation(fields: [interviewGroupId], references: [interviewGroupId], onDelete: Cascade)
  aIBotTestRoom        AIBotTestRoom?
  @@index([interviewGroupId])
}

enum InterviewCandidateStatus {
  PROCESSING
  HIRED
  REJECTED
}

enum HandleScoringStatus {
  PROCESSING
  FAILED
  DONE
  PENDING
}

model CustomQuestion {
  customQuestionId String         @id @default(uuid())
  interviewGroupId String
  question         String         @db.VarChar(500)
  answer           String?        @db.VarChar(1000)
  order            Int            @default(0)
  isRequired       Boolean        @default(true)
  duration         Int?           @default(120) // Duration in seconds, default to 2 minutes (120 seconds)
  createdAt        DateTime       @default(now())
  updatedAt        DateTime       @updatedAt
  interviewGroup   InterviewGroup @relation(fields: [interviewGroupId], references: [interviewGroupId], onDelete: Cascade)

  @@index([interviewGroupId])
}
model MeetingTranscription {
  meetingTranscriptionId Int           @id @default(autoincrement())
  role                   String?
  content                String?
  createdAt              DateTime      @default(now())
  
  aIBotTestRoomId        Int
  aIBotTestRoom          AIBotTestRoom @relation(fields: [aIBotTestRoomId], references: [aIBotTestRoomId])
}

model MeetingScoring {
  meetingScoringId   Int               @id @default(autoincrement())
  fluencyAndCoherence String?
  lexicalResource     String?
  grammaticalRangeAndAccuracy String?
  CEFRStandard        String?
  createdAt           DateTime         @default(now())
  updatedAt           DateTime         @updatedAt
  feedbacks           MeetingFeedback[]
  aIBotTestRoomId               Int               @unique
  aIBotTestRoom                 AIBotTestRoom     @relation("RoomScoring", fields: [aIBotTestRoomId], references: [aIBotTestRoomId])
}

model MeetingFeedback {
  meetingFeedbackId Int       @id @default(autoincrement())
  aiAssessment      String?
  scoringLevel      String?
  question          String?
  idealAnswer       String?
  userAnswer        String?
  meetingScoringId  Int
  meetingScoring    MeetingScoring @relation(fields: [meetingScoringId], references: [meetingScoringId])
}

model ClientPermission {
  clientPermissionId   Int      @id @default(autoincrement())
  userId               Int      @unique
  isCanCustomInterview Boolean  @default(false)
  isCanShortlist       Boolean  @default(false)
  isCanJobOpenings     Boolean  @default(false)
  createdAt            DateTime @default(now())
  updatedAt            DateTime @updatedAt

  user User @relation(fields: [userId], references: [userId], onDelete: Cascade)

  @@index([userId])
}

model JobLocation {
  jobLocationId Int      @id @default(autoincrement())
  countryId     Int?
  cityId        Int?
  country       Country? @relation(fields: [countryId], references: [countryId])
  city          City?    @relation(fields: [cityId], references: [cityId])
  createdAt     DateTime @default(now())
  updatedAt     DateTime @updatedAt
  jobs          Job[]
}