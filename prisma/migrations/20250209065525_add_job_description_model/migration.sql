-- CreateE<PERSON>
CREATE TYPE "JobDescriptionStatus" AS ENUM ('CLOSED', 'DRAFT', 'UNDER_REVIEW', 'OPEN');

-- CreateTable
CREATE TABLE "JobDescription" (
    "id" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "createdBy" TEXT NOT NULL,
    "updatedBy" TEXT NOT NULL,
    "companyOverview" TEXT NOT NULL,
    "commitment" TEXT NOT NULL,
    "level" TEXT NOT NULL,
    "jobTitle" TEXT NOT NULL,
    "yearsExperience" TEXT NOT NULL,
    "skillsRequired" TEXT NOT NULL,
    "roleExpectation" TEXT NOT NULL,
    "mustHave" TEXT NOT NULL,
    "niceToHave" TEXT NOT NULL,
    "status" "JobDescriptionStatus" NOT NULL DEFAULT 'DRAFT',

    CONSTRAINT "JobDescription_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "JobDescriptionHistory" (
    "id" TEXT NOT NULL,
    "job_description_id" TEXT NOT NULL,
    "updatedBy" TEXT NOT NULL,
    "changes" JSONB NOT NULL,
    "updatedAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "JobDescriptionHistory_pkey" PRIMARY KEY ("id")
);

-- AddForeignKey
ALTER TABLE "JobDescriptionHistory" ADD CONSTRAINT "JobDescriptionHistory_job_description_id_fkey" FOREIGN KEY ("job_description_id") REFERENCES "JobDescription"("id") ON DELETE CASCADE ON UPDATE CASCADE;
