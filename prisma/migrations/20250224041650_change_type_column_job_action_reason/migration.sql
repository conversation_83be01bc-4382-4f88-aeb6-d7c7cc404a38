/*
  Warnings:

  - You are about to drop the column `JobActionReason` on the `Job` table. All the data in the column will be lost.
  - The primary key for the `JobActionReason` table will be changed. If it partially fails, the table could be left without primary key constraint.
  - The `id` column on the `JobActionReason` table would be dropped and recreated. This will lead to data loss if there is data in the column.

*/
-- AlterTable
ALTER TABLE "Job" DROP COLUMN "JobActionReason",
ADD COLUMN     "JobActionReasonid" INTEGER;

-- AlterTable
ALTER TABLE "JobActionReason" DROP CONSTRAINT "JobActionReason_pkey",
DROP COLUMN "id",
ADD COLUMN     "id" SERIAL NOT NULL,
ADD CONSTRAINT "JobActionReason_pkey" PRIMARY KEY ("id");

-- Add<PERSON>oreign<PERSON>ey
ALTER TABLE "Job" ADD CONSTRAINT "Job_JobActionReasonid_fkey" FOREIGN KEY ("JobActionReasonid") REFERENCES "JobActionReason"("id") ON DELETE SET NULL ON UPDATE CASCADE;
