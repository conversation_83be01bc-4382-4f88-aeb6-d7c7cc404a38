/*
  Warnings:

  - You are about to drop the column `interviewCandidateId` on the `MasterForm` table. All the data in the column will be lost.
  - A unique constraint covering the columns `[interviewCandidateId]` on the table `AIBotTestRoom` will be added. If there are existing duplicate values, this will fail.

*/
-- DropForeignKey
ALTER TABLE "MasterForm" DROP CONSTRAINT "MasterForm_interviewCandidateId_fkey";

-- DropIndex
DROP INDEX "MasterForm_interviewCandidateId_key";

-- AlterTable
ALTER TABLE "AIBotTestRoom" ADD COLUMN     "interviewCandidateId" TEXT;

-- AlterTable
ALTER TABLE "MasterForm" DROP COLUMN "interviewCandidateId";

-- CreateIndex
CREATE UNIQUE INDEX "AIBotTestRoom_interviewCandidateId_key" ON "AIBotTestRoom"("interviewCandidateId");

-- AddForeignKey
ALTER TABLE "AIBotTestRoom" ADD CONSTRAINT "AIBotTestRoom_interviewCandidateId_fkey" FOREIGN KEY ("interviewCandidateId") REFERENCES "InterviewCandidate"("interviewCandidateId") ON DELETE CASCADE ON UPDATE CASCADE;
