-- AlterEnum
-- This migration adds more than one value to an enum.
-- With PostgreSQL versions 11 and earlier, this is not possible
-- in a single migration. This can be worked around by creating
-- multiple migrations, each migration adding only one value to
-- the enum.


ALTER TYPE "ProfileStatus" ADD VALUE 'PASSED_AI_INTERVIEW';
ALTER TYPE "ProfileStatus" ADD VALUE 'WORK_PREFERENCE_SPECIFIED';
ALTER TYPE "ProfileStatus" ADD VALUE 'PASSED_HUMAN_INTERVIEW';

-- AlterTable
ALTER TABLE "Application" ADD COLUMN     "rejectionReason" TEXT;
