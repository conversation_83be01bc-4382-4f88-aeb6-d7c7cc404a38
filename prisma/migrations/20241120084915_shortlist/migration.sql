-- CreateEnum
CREATE TYPE "ShortListStatus" AS ENUM ('DRAFTED', 'PUBLISHED');

-- CreateTable
CREATE TABLE "ShortList" (
    "shortListId" SERIAL NOT NULL,
    "jobId" INTEGER NOT NULL,
    "status" "ShortListStatus" NOT NULL,
    "isActive" BOOLEAN NOT NULL DEFAULT false,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "ShortList_pkey" PRIMARY KEY ("shortListId")
);

-- CreateTable
CREATE TABLE "PotentialCandidate" (
    "potentialCandidateId" SERIAL NOT NULL,
    "userId" INTEGER NOT NULL,
    "shortListId" INTEGER NOT NULL,
    "isActive" BOOLEAN NOT NULL DEFAULT false,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "PotentialCandidate_pkey" PRIMARY KEY ("potentialCandidateId")
);

-- CreateIndex
CREATE UNIQUE INDEX "ShortList_jobId_key" ON "ShortList"("jobId");

-- CreateIndex
CREATE UNIQUE INDEX "PotentialCandidate_userId_shortListId_key" ON "PotentialCandidate"("userId", "shortListId");

-- AddForeignKey
ALTER TABLE "ShortList" ADD CONSTRAINT "ShortList_jobId_fkey" FOREIGN KEY ("jobId") REFERENCES "Job"("jobId") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "PotentialCandidate" ADD CONSTRAINT "PotentialCandidate_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("userId") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "PotentialCandidate" ADD CONSTRAINT "PotentialCandidate_shortListId_fkey" FOREIGN KEY ("shortListId") REFERENCES "ShortList"("shortListId") ON DELETE RESTRICT ON UPDATE CASCADE;
