/*
  Warnings:

  - The values [PENDING,COMPLETED] on the enum `AIBotTestRoomStatus` will be removed. If these variants are still used in the database, this will fail.

*/
-- AlterEnum
BEGIN;
CREATE TYPE "AIBotTestRoomStatus_new" AS ENUM ('ROOM_CREATED', 'SCANNING', 'SCAN_COMPLETED');
ALTER TABLE "AIBotTestRoom" ALTER COLUMN "roomStatus" TYPE "AIBotTestRoomStatus_new" USING ("roomStatus"::text::"AIBotTestRoomStatus_new");
ALTER TYPE "AIBotTestRoomStatus" RENAME TO "AIBotTestRoomStatus_old";
ALTER TYPE "AIBotTestRoomStatus_new" RENAME TO "AIBotTestRoomStatus";
DROP TYPE "AIBotTestRoomStatus_old";
COMMIT;
