/*
  Warnings:

  - You are about to drop the column `requiredSkills` on the `Job` table. All the data in the column will be lost.

*/
-- AlterTable
ALTER TABLE "Job" DROP COLUMN "requiredSkills";

-- CreateTable
CREATE TABLE "_JobToTechnicalSkill" (
    "A" INTEGER NOT NULL,
    "B" INTEGER NOT NULL
);

-- CreateIndex
CREATE UNIQUE INDEX "_JobToTechnicalSkill_AB_unique" ON "_JobToTechnicalSkill"("A", "B");

-- CreateIndex
CREATE INDEX "_JobToTechnicalSkill_B_index" ON "_JobToTechnicalSkill"("B");

-- AddForeignKey
ALTER TABLE "_JobToTechnicalSkill" ADD CONSTRAINT "_JobToTechnicalSkill_A_fkey" FOREIGN KEY ("A") REFERENCES "Job"("jobId") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "_JobToTechnicalSkill" ADD CONSTRAINT "_JobToTechnicalSkill_B_fkey" FOREIGN KEY ("B") REFERENCES "TechnicalSkill"("technicalSkillId") ON DELETE CASCADE ON UPDATE CASCADE;
