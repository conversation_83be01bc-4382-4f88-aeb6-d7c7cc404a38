-- CreateTable
CREATE TABLE "TokenUsageLog" (
    "id" SERIAL NOT NULL,
    "taskId" INTEGER NOT NULL,
    "jobId" INTEGER NOT NULL,
    "profileId" INTEGER NOT NULL,
    "inputTokens" INTEGER NOT NULL,
    "outputTokens" INTEGER NOT NULL,
    "requestType" TEXT NOT NULL,
    "inputTokenDetail" JSONB,
    "outputTokenDetail" JSONB,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "TokenUsageLog_pkey" PRIMARY KEY ("id")
);
