-- CreateE<PERSON>
CREATE TYPE "InterviewGroupStatus" AS ENUM ('O<PERSON><PERSON>', 'CLOSED');

-- CreateTable
CREATE TABLE "InterviewGroup" (
    "interviewGroupId" TEXT NOT NULL,
    "jobId" INTEGER NOT NULL,
    "name" TEXT NOT NULL,
    "status" "InterviewGroupStatus" NOT NULL DEFAULT 'OPEN',
    "totalDuration" INTEGER,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "InterviewGroup_pkey" PRIMARY KEY ("interviewGroupId")
);

-- CreateIndex
CREATE INDEX "InterviewGroup_jobId_idx" ON "InterviewGroup"("jobId");

-- AddF<PERSON>ign<PERSON>ey
ALTER TABLE "InterviewGroup" ADD CONSTRAINT "InterviewGroup_jobId_fkey" FOREIGN KEY ("jobId") REFERENCES "Job"("jobId") ON DELETE CASCADE ON UPDATE CASCADE;
