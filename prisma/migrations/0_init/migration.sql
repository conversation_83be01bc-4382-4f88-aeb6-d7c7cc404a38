-- Create<PERSON><PERSON>
CREATE TYPE "Proficiency" AS ENUM ('Beginner', 'Intermediate', 'Advanced', 'Native');

-- <PERSON><PERSON><PERSON>num
CREATE TYPE "ApplicationStatus" AS ENUM ('APPLICATION_PENDING', 'APPLICATION_REJECTED', 'APPLICATION_APPROVED');

-- <PERSON><PERSON><PERSON><PERSON>
CREATE TYPE "TestStatus" AS ENUM ('TEST_PENDING', 'TEST_COMPLETED', 'TEST_PASSED', 'TEST_FAILED', 'TEST_NOTIFIED', 'GREY_ZONE');

-- CreateEnum
CREATE TYPE "CompanySize" AS ENUM ('SMALL', 'MEDIUM', 'LARGE', 'ENTERPRISE');

-- Create<PERSON>num
CREATE TYPE "MeetingStatus" AS ENUM ('SCHEDULING', 'SUCCESSFULLY_SCHEDULED', 'FAILED_TO_SCHEDULE', 'CANCELLED');

-- <PERSON><PERSON><PERSON><PERSON>
CREATE TYPE "TaskStatus" AS ENUM ('SCHEDULED', 'RUNNING', 'CANCELLED', 'COMPLETE', 'ERROR');

-- <PERSON>reateTable
CREATE TABLE "User" (
    "userId" SERIAL NOT NULL,
    "email" TEXT NOT NULL,
    "passwordHash" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "User_pkey" PRIMARY KEY ("userId")
);

-- CreateTable
CREATE TABLE "Language" (
    "languageId" SERIAL NOT NULL,
    "languageName" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "Language_pkey" PRIMARY KEY ("languageId")
);

-- CreateTable
CREATE TABLE "UserLanguage" (
    "userId" INTEGER NOT NULL,
    "languageId" INTEGER NOT NULL,
    "proficiency" "Proficiency" NOT NULL,
    "roleId" INTEGER NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "UserLanguage_pkey" PRIMARY KEY ("userId","roleId","languageId")
);

-- CreateTable
CREATE TABLE "Company" (
    "name" TEXT NOT NULL,
    "website" TEXT,
    "description" TEXT,
    "location" TEXT,
    "industryId" INTEGER,
    "companyId" SERIAL NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "Company_pkey" PRIMARY KEY ("companyId")
);

-- CreateTable
CREATE TABLE "Country" (
    "countryId" SERIAL NOT NULL,
    "countryName" TEXT NOT NULL,
    "flagImage" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "code" TEXT,

    CONSTRAINT "Country_pkey" PRIMARY KEY ("countryId")
);

-- CreateTable
CREATE TABLE "Role" (
    "roleId" SERIAL NOT NULL,
    "roleName" TEXT NOT NULL,

    CONSTRAINT "Role_pkey" PRIMARY KEY ("roleId")
);

-- CreateTable
CREATE TABLE "UserRole" (
    "userId" INTEGER NOT NULL,
    "roleId" INTEGER NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "UserRole_pkey" PRIMARY KEY ("userId","roleId")
);

-- CreateTable
CREATE TABLE "Profile" (
    "profileId" SERIAL NOT NULL,
    "userId" INTEGER NOT NULL,
    "address" TEXT,
    "birthDate" TIMESTAMP(3),
    "companyId" INTEGER,
    "countryId" INTEGER,
    "currentSalary" DOUBLE PRECISION,
    "email" TEXT[],
    "expectedSalary" DOUBLE PRECISION,
    "firstName" TEXT NOT NULL,
    "idCard" TEXT[],
    "lastName" TEXT NOT NULL,
    "minSalary" DOUBLE PRECISION,
    "phoneNumber" TEXT,
    "profilePhoto" TEXT,
    "socialLink" TEXT[],
    "cv" TEXT[],
    "roleId" INTEGER NOT NULL,
    "currencyId" INTEGER,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "Profile_pkey" PRIMARY KEY ("profileId")
);

-- CreateTable
CREATE TABLE "Job" (
    "jobId" SERIAL NOT NULL,
    "userId" INTEGER NOT NULL,
    "roleId" INTEGER NOT NULL,
    "title" TEXT NOT NULL,
    "description" TEXT NOT NULL,
    "industryId" INTEGER,
    "location" TEXT NOT NULL,
    "workingSchedule" TEXT,
    "startingSalary" DOUBLE PRECISION NOT NULL,
    "endingSalary" DOUBLE PRECISION NOT NULL,
    "currencySalaryId" INTEGER NOT NULL,
    "postedDate" TIMESTAMP(3) NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "benefit" TEXT NOT NULL,
    "requirement" TEXT NOT NULL,
    "responsibility" TEXT NOT NULL,
    "isActive" BOOLEAN DEFAULT true,

    CONSTRAINT "Job_pkey" PRIMARY KEY ("jobId")
);

-- CreateTable
CREATE TABLE "Application" (
    "applicationId" SERIAL NOT NULL,
    "jobId" INTEGER NOT NULL,
    "appliedDate" TIMESTAMP(3) NOT NULL,
    "roleId" INTEGER NOT NULL,
    "userId" INTEGER NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "status" "ApplicationStatus" NOT NULL,

    CONSTRAINT "Application_pkey" PRIMARY KEY ("applicationId")
);

-- CreateTable
CREATE TABLE "FoundationTest" (
    "foundationTestId" SERIAL NOT NULL,
    "applicationId" INTEGER NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "status" "TestStatus" NOT NULL,

    CONSTRAINT "FoundationTest_pkey" PRIMARY KEY ("foundationTestId")
);

-- CreateTable
CREATE TABLE "EnglishTest" (
    "englishTestId" SERIAL NOT NULL,
    "foundationTestId" INTEGER NOT NULL,
    "question" TEXT NOT NULL,
    "userAnswer" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "score" INTEGER NOT NULL,
    "status" "TestStatus" NOT NULL,
    "screenRecordUrl" TEXT,
    "webcamRecordUrl" TEXT,

    CONSTRAINT "EnglishTest_pkey" PRIMARY KEY ("englishTestId")
);

-- CreateTable
CREATE TABLE "TechnicalTest" (
    "technicalTestId" SERIAL NOT NULL,
    "foundationTestId" INTEGER NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "score" INTEGER NOT NULL,
    "status" "TestStatus" NOT NULL,
    "screenRecordUrl" TEXT,
    "webcamRecordUrl" TEXT,

    CONSTRAINT "TechnicalTest_pkey" PRIMARY KEY ("technicalTestId")
);

-- CreateTable
CREATE TABLE "TechnicalTestQuestion" (
    "technicalTestQuestionId" SERIAL NOT NULL,
    "interviewQuestionId" INTEGER NOT NULL,
    "technicalTestId" INTEGER,
    "answer" TEXT NOT NULL,
    "score" INTEGER NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "TechnicalTestQuestion_pkey" PRIMARY KEY ("technicalTestQuestionId")
);

-- CreateTable
CREATE TABLE "AIBotTestQuestion" (
    "aiBotTestQuestionId" SERIAL NOT NULL,
    "aiBotTestId" INTEGER,
    "messageId" TEXT,
    "audioRecordUrl" TEXT,
    "question" TEXT NOT NULL,
    "answer" TEXT NOT NULL,
    "score" INTEGER NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "AIBotTestQuestion_pkey" PRIMARY KEY ("aiBotTestQuestionId")
);

-- CreateTable
CREATE TABLE "CodingQuestion" (
    "codingQuestionId" SERIAL NOT NULL,
    "interviewQuestionId" INTEGER NOT NULL,
    "codingTestId" INTEGER,
    "answer" TEXT NOT NULL,
    "score" INTEGER,
    "compileResult" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "compileResultId" INTEGER,
    "codingLanguageId" INTEGER,
    "codingLanguage" TEXT,

    CONSTRAINT "CodingQuestion_pkey" PRIMARY KEY ("codingQuestionId")
);

-- CreateTable
CREATE TABLE "CodingTest" (
    "codingTestId" SERIAL NOT NULL,
    "foundationTestId" INTEGER,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "score" INTEGER NOT NULL,
    "screenRecordUrl" TEXT,
    "webcamRecordUrl" TEXT,
    "status" "TestStatus" NOT NULL,

    CONSTRAINT "CodingTest_pkey" PRIMARY KEY ("codingTestId")
);

-- CreateTable
CREATE TABLE "AIBotTest" (
    "aiBotTestId" SERIAL NOT NULL,
    "foundationTestId" INTEGER,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "score" INTEGER NOT NULL,
    "status" "TestStatus" NOT NULL,
    "conversationId" TEXT,
    "screenRecordUrl" TEXT,
    "audioRecordUrl" TEXT,

    CONSTRAINT "AIBotTest_pkey" PRIMARY KEY ("aiBotTestId")
);

-- CreateTable
CREATE TABLE "Interview" (
    "interviewId" SERIAL NOT NULL,
    "applicationId" INTEGER NOT NULL,
    "schedule" TIMESTAMP(3) NOT NULL,
    "format" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "Interview_pkey" PRIMARY KEY ("interviewId")
);

-- CreateTable
CREATE TABLE "Message" (
    "messageId" SERIAL NOT NULL,
    "content" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "authorId" INTEGER NOT NULL,
    "authorRoleId" INTEGER NOT NULL,
    "conversationId" INTEGER NOT NULL,

    CONSTRAINT "Message_pkey" PRIMARY KEY ("messageId")
);

-- CreateTable
CREATE TABLE "Conversation" (
    "conversationId" SERIAL NOT NULL,
    "creatorId" INTEGER NOT NULL,
    "recipientId" INTEGER NOT NULL,
    "creatorRoleId" INTEGER NOT NULL,
    "recipientRoleId" INTEGER NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "Conversation_pkey" PRIMARY KEY ("conversationId")
);

-- CreateTable
CREATE TABLE "Notification" (
    "notificationId" SERIAL NOT NULL,
    "userId" INTEGER NOT NULL,
    "content" TEXT NOT NULL,
    "roleId" INTEGER NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "Notification_pkey" PRIMARY KEY ("notificationId")
);

-- CreateTable
CREATE TABLE "Report" (
    "reportId" SERIAL NOT NULL,
    "recruiterId" INTEGER NOT NULL,
    "type" TEXT NOT NULL,
    "generatedDate" TIMESTAMP(3) NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "Report_pkey" PRIMARY KEY ("reportId")
);

-- CreateTable
CREATE TABLE "Industry" (
    "industryId" SERIAL NOT NULL,
    "name" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "Industry_pkey" PRIMARY KEY ("industryId")
);

-- CreateTable
CREATE TABLE "SkillLevel" (
    "skillLevelId" SERIAL NOT NULL,
    "name" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "SkillLevel_pkey" PRIMARY KEY ("skillLevelId")
);

-- CreateTable
CREATE TABLE "JobSkill" (
    "jobId" INTEGER NOT NULL,
    "skillLevelId" INTEGER NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "JobSkill_pkey" PRIMARY KEY ("jobId","skillLevelId")
);

-- CreateTable
CREATE TABLE "EmploymentType" (
    "id" SERIAL NOT NULL,
    "name" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "EmploymentType_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "JobEmploymentType" (
    "id" SERIAL NOT NULL,
    "jobId" INTEGER NOT NULL,
    "employmentId" INTEGER NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "JobEmploymentType_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "Currency" (
    "currencyId" SERIAL NOT NULL,
    "code" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "Currency_pkey" PRIMARY KEY ("currencyId")
);

-- CreateTable
CREATE TABLE "JobLanguageSkill" (
    "languageId" INTEGER NOT NULL,
    "jobId" INTEGER NOT NULL,
    "proficiency" "Proficiency" NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "JobLanguageSkill_pkey" PRIMARY KEY ("jobId","languageId")
);

-- CreateTable
CREATE TABLE "AffiliateReward" (
    "affiliateRewardId" SERIAL NOT NULL,
    "referrerCompanyId" INTEGER NOT NULL,
    "referredCompanyId" INTEGER NOT NULL,
    "currencyId" INTEGER NOT NULL,
    "percentage" DECIMAL(65,30) NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "AffiliateReward_pkey" PRIMARY KEY ("affiliateRewardId")
);

-- CreateTable
CREATE TABLE "CouponCode" (
    "id" SERIAL NOT NULL,
    "companyId" INTEGER NOT NULL,
    "code" TEXT NOT NULL,
    "discountAmount" DECIMAL(65,30) NOT NULL,
    "currencyId" INTEGER NOT NULL,
    "validFrom" TIMESTAMP(3) NOT NULL,
    "validTo" TIMESTAMP(3) NOT NULL,
    "usageDurationDays" INTEGER NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "CouponCode_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "AffiliateRewardTransaction" (
    "id" SERIAL NOT NULL,
    "rewardId" INTEGER NOT NULL,
    "amount" DECIMAL(65,30) NOT NULL,
    "currencyId" INTEGER NOT NULL,
    "date" TIMESTAMP(3) NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "AffiliateRewardTransaction_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "CouponRedemptionTransaction" (
    "id" SERIAL NOT NULL,
    "couponCodeId" INTEGER NOT NULL,
    "amount" DECIMAL(65,30) NOT NULL,
    "currencyId" INTEGER NOT NULL,
    "date" TIMESTAMP(3) NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "CouponRedemptionTransaction_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "Keyword" (
    "content" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "keywordId" SERIAL NOT NULL,

    CONSTRAINT "Keyword_pkey" PRIMARY KEY ("keywordId")
);

-- CreateTable
CREATE TABLE "UserKeyword" (
    "profileId" INTEGER NOT NULL,
    "keywordId" INTEGER NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "UserKeyword_pkey" PRIMARY KEY ("profileId","keywordId")
);

-- CreateTable
CREATE TABLE "JobKeyword" (
    "jobId" INTEGER NOT NULL,
    "keywordId" INTEGER NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "JobKeyword_pkey" PRIMARY KEY ("jobId","keywordId")
);

-- CreateTable
CREATE TABLE "TypeDegree" (
    "typeDegreeId" SERIAL NOT NULL,
    "name" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "TypeDegree_pkey" PRIMARY KEY ("typeDegreeId")
);

-- CreateTable
CREATE TABLE "AcademyHistory" (
    "profileId" INTEGER NOT NULL,
    "universityName" TEXT NOT NULL,
    "major" TEXT,
    "startDate" TIMESTAMP(3),
    "endDate" TIMESTAMP(3),
    "degreeId" INTEGER,
    "proof" TEXT[],
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "academyHistoryId" SERIAL NOT NULL,

    CONSTRAINT "AcademyHistory_pkey" PRIMARY KEY ("academyHistoryId")
);

-- CreateTable
CREATE TABLE "EmploymentHistory" (
    "profileId" INTEGER NOT NULL,
    "companyName" TEXT NOT NULL,
    "startDate" TIMESTAMP(3),
    "endDate" TIMESTAMP(3),
    "currentlyWorking" BOOLEAN,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "employmentHistoryId" SERIAL NOT NULL,

    CONSTRAINT "EmploymentHistory_pkey" PRIMARY KEY ("employmentHistoryId")
);

-- CreateTable
CREATE TABLE "WorkingPositionHistory" (
    "employmentHistoryId" INTEGER NOT NULL,
    "position" TEXT NOT NULL,
    "positionStartDate" TIMESTAMP(3),
    "positionEndDate" TIMESTAMP(3),
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "workingPositionHistoryId" SERIAL NOT NULL,
    "skills" TEXT,

    CONSTRAINT "WorkingPositionHistory_pkey" PRIMARY KEY ("workingPositionHistoryId")
);

-- CreateTable
CREATE TABLE "InterviewQuestion" (
    "question" TEXT NOT NULL,
    "answer" TEXT NOT NULL,
    "level" TEXT NOT NULL,
    "skill" TEXT NOT NULL,
    "category" TEXT NOT NULL,
    "source" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3),
    "interviewQuestionId" SERIAL NOT NULL,
    "testCases" TEXT,
    "codeTemplate" TEXT,
    "codeTestCases" TEXT,

    CONSTRAINT "InterviewQuestion_pkey" PRIMARY KEY ("interviewQuestionId")
);

-- CreateTable
CREATE TABLE "CVTask" (
    "taskId" SERIAL NOT NULL,
    "jobId" INTEGER,
    "title" TEXT,
    "description" TEXT,
    "status" "TaskStatus" NOT NULL,
    "scanAllCandidates" BOOLEAN NOT NULL DEFAULT false,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "CVTask_pkey" PRIMARY KEY ("taskId")
);

-- CreateTable
CREATE TABLE "CVTaskCandidate" (
    "taskId" INTEGER NOT NULL,
    "profileId" INTEGER NOT NULL,
    "score" INTEGER,
    "isActive" BOOLEAN DEFAULT true,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "CVTaskCandidate_pkey" PRIMARY KEY ("taskId","profileId")
);

-- CreateTable
CREATE TABLE "CodeTemplate" (
    "codeTemplateId" SERIAL NOT NULL,
    "interviewQuestionId" INTEGER,
    "codingLanguageId" INTEGER,
    "codingLanguage" TEXT,
    "testCases" TEXT,
    "codeTemplate" TEXT,
    "codeTestCases" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "CodeTemplate_pkey" PRIMARY KEY ("codeTemplateId")
);

-- CreateTable
CREATE TABLE "BusyTime" (
    "busyTimeId" SERIAL NOT NULL,
    "meetingUserId" INTEGER NOT NULL,
    "startTime" TIMESTAMP(3) NOT NULL,
    "endTime" TIMESTAMP(3) NOT NULL,
    "isActive" BOOLEAN DEFAULT true,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "BusyTime_pkey" PRIMARY KEY ("busyTimeId")
);

-- CreateTable
CREATE TABLE "Meeting" (
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "customerId" INTEGER NOT NULL,
    "endTime" TIMESTAMP(3) NOT NULL,
    "hostId" INTEGER NOT NULL,
    "linkMeeting" TEXT,
    "meetingId" SERIAL NOT NULL,
    "calendarId" TEXT,
    "calendarEventId" TEXT,
    "startTime" TIMESTAMP(3) NOT NULL,
    "timezone" TEXT,
    "status" "MeetingStatus" NOT NULL,

    CONSTRAINT "Meeting_pkey" PRIMARY KEY ("meetingId")
);

-- CreateTable
CREATE TABLE "MeetingUser" (
    "meetingUserId" SERIAL NOT NULL,
    "firstName" TEXT NOT NULL,
    "lastName" TEXT NOT NULL,
    "workEmail" TEXT NOT NULL,
    "priority" INTEGER,
    "timezone" TEXT,
    "companySize" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "isHost" BOOLEAN NOT NULL,
    "headquarterId" INTEGER,
    "stateHeadquarter" TEXT,
    "phoneNumber" TEXT,
    "website" TEXT,

    CONSTRAINT "MeetingUser_pkey" PRIMARY KEY ("meetingUserId")
);

-- CreateIndex
CREATE UNIQUE INDEX "User_email_key" ON "User"("email");

-- CreateIndex
CREATE UNIQUE INDEX "Role_roleName_key" ON "Role"("roleName");

-- CreateIndex
CREATE UNIQUE INDEX "Profile_userId_roleId_key" ON "Profile"("userId", "roleId");

-- CreateIndex
CREATE UNIQUE INDEX "Application_jobId_userId_roleId_key" ON "Application"("jobId", "userId", "roleId");

-- CreateIndex
CREATE UNIQUE INDEX "FoundationTest_applicationId_key" ON "FoundationTest"("applicationId");

-- CreateIndex
CREATE UNIQUE INDEX "EnglishTest_foundationTestId_key" ON "EnglishTest"("foundationTestId");

-- CreateIndex
CREATE UNIQUE INDEX "TechnicalTest_foundationTestId_key" ON "TechnicalTest"("foundationTestId");

-- CreateIndex
CREATE UNIQUE INDEX "CodingTest_foundationTestId_key" ON "CodingTest"("foundationTestId");

-- CreateIndex
CREATE UNIQUE INDEX "AIBotTest_foundationTestId_key" ON "AIBotTest"("foundationTestId");

-- CreateIndex
CREATE UNIQUE INDEX "Currency_code_key" ON "Currency"("code");

-- CreateIndex
CREATE UNIQUE INDEX "CouponCode_code_key" ON "CouponCode"("code");

-- CreateIndex
CREATE UNIQUE INDEX "AffiliateRewardTransaction_rewardId_key" ON "AffiliateRewardTransaction"("rewardId");

-- CreateIndex
CREATE UNIQUE INDEX "CouponRedemptionTransaction_couponCodeId_key" ON "CouponRedemptionTransaction"("couponCodeId");

-- CreateIndex
CREATE INDEX "CVTask_jobId_idx" ON "CVTask"("jobId");

-- CreateIndex
CREATE INDEX "CVTaskCandidate_taskId_idx" ON "CVTaskCandidate"("taskId");

-- AddForeignKey
ALTER TABLE "UserLanguage" ADD CONSTRAINT "UserLanguage_languageId_fkey" FOREIGN KEY ("languageId") REFERENCES "Language"("languageId") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "UserLanguage" ADD CONSTRAINT "UserLanguage_userId_roleId_fkey" FOREIGN KEY ("userId", "roleId") REFERENCES "UserRole"("userId", "roleId") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Company" ADD CONSTRAINT "Company_industryId_fkey" FOREIGN KEY ("industryId") REFERENCES "Industry"("industryId") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "UserRole" ADD CONSTRAINT "UserRole_roleId_fkey" FOREIGN KEY ("roleId") REFERENCES "Role"("roleId") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "UserRole" ADD CONSTRAINT "UserRole_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("userId") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Profile" ADD CONSTRAINT "Profile_companyId_fkey" FOREIGN KEY ("companyId") REFERENCES "Company"("companyId") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Profile" ADD CONSTRAINT "Profile_countryId_fkey" FOREIGN KEY ("countryId") REFERENCES "Country"("countryId") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Profile" ADD CONSTRAINT "Profile_currencyId_fkey" FOREIGN KEY ("currencyId") REFERENCES "Currency"("currencyId") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Profile" ADD CONSTRAINT "Profile_userId_roleId_fkey" FOREIGN KEY ("userId", "roleId") REFERENCES "UserRole"("userId", "roleId") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Job" ADD CONSTRAINT "Job_currencySalaryId_fkey" FOREIGN KEY ("currencySalaryId") REFERENCES "Currency"("currencyId") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Job" ADD CONSTRAINT "Job_industryId_fkey" FOREIGN KEY ("industryId") REFERENCES "Industry"("industryId") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Job" ADD CONSTRAINT "Job_userId_roleId_fkey" FOREIGN KEY ("userId", "roleId") REFERENCES "UserRole"("userId", "roleId") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Application" ADD CONSTRAINT "Application_jobId_fkey" FOREIGN KEY ("jobId") REFERENCES "Job"("jobId") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Application" ADD CONSTRAINT "Application_userId_roleId_fkey" FOREIGN KEY ("userId", "roleId") REFERENCES "UserRole"("userId", "roleId") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "FoundationTest" ADD CONSTRAINT "FoundationTest_applicationId_fkey" FOREIGN KEY ("applicationId") REFERENCES "Application"("applicationId") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "EnglishTest" ADD CONSTRAINT "EnglishTest_foundationTestId_fkey" FOREIGN KEY ("foundationTestId") REFERENCES "FoundationTest"("foundationTestId") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "TechnicalTest" ADD CONSTRAINT "TechnicalTest_foundationTestId_fkey" FOREIGN KEY ("foundationTestId") REFERENCES "FoundationTest"("foundationTestId") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "TechnicalTestQuestion" ADD CONSTRAINT "TechnicalTestQuestion_interviewQuestionId_fkey" FOREIGN KEY ("interviewQuestionId") REFERENCES "InterviewQuestion"("interviewQuestionId") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "TechnicalTestQuestion" ADD CONSTRAINT "TechnicalTestQuestion_technicalTestId_fkey" FOREIGN KEY ("technicalTestId") REFERENCES "TechnicalTest"("technicalTestId") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "AIBotTestQuestion" ADD CONSTRAINT "AIBotTestQuestion_aiBotTestId_fkey" FOREIGN KEY ("aiBotTestId") REFERENCES "AIBotTest"("aiBotTestId") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "CodingQuestion" ADD CONSTRAINT "CodingQuestion_codingTestId_fkey" FOREIGN KEY ("codingTestId") REFERENCES "CodingTest"("codingTestId") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "CodingQuestion" ADD CONSTRAINT "CodingQuestion_interviewQuestionId_fkey" FOREIGN KEY ("interviewQuestionId") REFERENCES "InterviewQuestion"("interviewQuestionId") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "CodingTest" ADD CONSTRAINT "CodingTest_foundationTestId_fkey" FOREIGN KEY ("foundationTestId") REFERENCES "FoundationTest"("foundationTestId") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "AIBotTest" ADD CONSTRAINT "AIBotTest_foundationTestId_fkey" FOREIGN KEY ("foundationTestId") REFERENCES "FoundationTest"("foundationTestId") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Interview" ADD CONSTRAINT "Interview_applicationId_fkey" FOREIGN KEY ("applicationId") REFERENCES "Application"("applicationId") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Message" ADD CONSTRAINT "Message_authorId_authorRoleId_fkey" FOREIGN KEY ("authorId", "authorRoleId") REFERENCES "UserRole"("userId", "roleId") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Message" ADD CONSTRAINT "Message_conversationId_fkey" FOREIGN KEY ("conversationId") REFERENCES "Conversation"("conversationId") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Conversation" ADD CONSTRAINT "Conversation_creatorId_creatorRoleId_fkey" FOREIGN KEY ("creatorId", "creatorRoleId") REFERENCES "UserRole"("userId", "roleId") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Conversation" ADD CONSTRAINT "Conversation_recipientId_recipientRoleId_fkey" FOREIGN KEY ("recipientId", "recipientRoleId") REFERENCES "UserRole"("userId", "roleId") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Notification" ADD CONSTRAINT "Notification_userId_roleId_fkey" FOREIGN KEY ("userId", "roleId") REFERENCES "UserRole"("userId", "roleId") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Report" ADD CONSTRAINT "Report_recruiterId_fkey" FOREIGN KEY ("recruiterId") REFERENCES "User"("userId") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "JobSkill" ADD CONSTRAINT "JobSkill_jobId_fkey" FOREIGN KEY ("jobId") REFERENCES "Job"("jobId") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "JobSkill" ADD CONSTRAINT "JobSkill_skillLevelId_fkey" FOREIGN KEY ("skillLevelId") REFERENCES "SkillLevel"("skillLevelId") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "JobEmploymentType" ADD CONSTRAINT "JobEmploymentType_employmentId_fkey" FOREIGN KEY ("employmentId") REFERENCES "EmploymentType"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "JobEmploymentType" ADD CONSTRAINT "JobEmploymentType_jobId_fkey" FOREIGN KEY ("jobId") REFERENCES "Job"("jobId") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "JobLanguageSkill" ADD CONSTRAINT "JobLanguageSkill_jobId_fkey" FOREIGN KEY ("jobId") REFERENCES "Job"("jobId") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "JobLanguageSkill" ADD CONSTRAINT "JobLanguageSkill_languageId_fkey" FOREIGN KEY ("languageId") REFERENCES "Language"("languageId") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "AffiliateReward" ADD CONSTRAINT "AffiliateReward_currencyId_fkey" FOREIGN KEY ("currencyId") REFERENCES "Currency"("currencyId") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "AffiliateReward" ADD CONSTRAINT "AffiliateReward_referredCompanyId_fkey" FOREIGN KEY ("referredCompanyId") REFERENCES "Company"("companyId") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "AffiliateReward" ADD CONSTRAINT "AffiliateReward_referrerCompanyId_fkey" FOREIGN KEY ("referrerCompanyId") REFERENCES "Company"("companyId") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "CouponCode" ADD CONSTRAINT "CouponCode_companyId_fkey" FOREIGN KEY ("companyId") REFERENCES "Company"("companyId") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "CouponCode" ADD CONSTRAINT "CouponCode_currencyId_fkey" FOREIGN KEY ("currencyId") REFERENCES "Currency"("currencyId") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "AffiliateRewardTransaction" ADD CONSTRAINT "AffiliateRewardTransaction_currencyId_fkey" FOREIGN KEY ("currencyId") REFERENCES "Currency"("currencyId") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "AffiliateRewardTransaction" ADD CONSTRAINT "AffiliateRewardTransaction_rewardId_fkey" FOREIGN KEY ("rewardId") REFERENCES "AffiliateReward"("affiliateRewardId") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "CouponRedemptionTransaction" ADD CONSTRAINT "CouponRedemptionTransaction_couponCodeId_fkey" FOREIGN KEY ("couponCodeId") REFERENCES "CouponCode"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "CouponRedemptionTransaction" ADD CONSTRAINT "CouponRedemptionTransaction_currencyId_fkey" FOREIGN KEY ("currencyId") REFERENCES "Currency"("currencyId") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "UserKeyword" ADD CONSTRAINT "UserKeyword_keywordId_fkey" FOREIGN KEY ("keywordId") REFERENCES "Keyword"("keywordId") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "UserKeyword" ADD CONSTRAINT "UserKeyword_profileId_fkey" FOREIGN KEY ("profileId") REFERENCES "Profile"("profileId") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "JobKeyword" ADD CONSTRAINT "JobKeyword_jobId_fkey" FOREIGN KEY ("jobId") REFERENCES "Job"("jobId") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "JobKeyword" ADD CONSTRAINT "JobKeyword_keywordId_fkey" FOREIGN KEY ("keywordId") REFERENCES "Keyword"("keywordId") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "AcademyHistory" ADD CONSTRAINT "AcademyHistory_degreeId_fkey" FOREIGN KEY ("degreeId") REFERENCES "TypeDegree"("typeDegreeId") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "AcademyHistory" ADD CONSTRAINT "AcademyHistory_profileId_fkey" FOREIGN KEY ("profileId") REFERENCES "Profile"("profileId") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "EmploymentHistory" ADD CONSTRAINT "EmploymentHistory_profileId_fkey" FOREIGN KEY ("profileId") REFERENCES "Profile"("profileId") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "WorkingPositionHistory" ADD CONSTRAINT "WorkingPositionHistory_employmentHistoryId_fkey" FOREIGN KEY ("employmentHistoryId") REFERENCES "EmploymentHistory"("employmentHistoryId") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "CVTask" ADD CONSTRAINT "CVTask_jobId_fkey" FOREIGN KEY ("jobId") REFERENCES "Job"("jobId") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "CVTaskCandidate" ADD CONSTRAINT "CVTaskCandidate_taskId_fkey" FOREIGN KEY ("taskId") REFERENCES "CVTask"("taskId") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "CVTaskCandidate" ADD CONSTRAINT "CVTaskCandidate_profileId_fkey" FOREIGN KEY ("profileId") REFERENCES "Profile"("profileId") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "CodeTemplate" ADD CONSTRAINT "CodeTemplate_interviewQuestionId_fkey" FOREIGN KEY ("interviewQuestionId") REFERENCES "InterviewQuestion"("interviewQuestionId") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "BusyTime" ADD CONSTRAINT "BusyTime_meetingUserId_fkey" FOREIGN KEY ("meetingUserId") REFERENCES "MeetingUser"("meetingUserId") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Meeting" ADD CONSTRAINT "Meeting_customerId_fkey" FOREIGN KEY ("customerId") REFERENCES "MeetingUser"("meetingUserId") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Meeting" ADD CONSTRAINT "Meeting_hostId_fkey" FOREIGN KEY ("hostId") REFERENCES "MeetingUser"("meetingUserId") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "MeetingUser" ADD CONSTRAINT "MeetingUser_headquarterId_fkey" FOREIGN KEY ("headquarterId") REFERENCES "Country"("countryId") ON DELETE SET NULL ON UPDATE CASCADE;

