/*
  Warnings:

  - You are about to drop the column `currency` on the `Business` table. All the data in the column will be lost.
  - You are about to drop the column `department` on the `Business` table. All the data in the column will be lost.
  - You are about to drop the column `industry` on the `Business` table. All the data in the column will be lost.
  - You are about to drop the column `jobTitle` on the `Business` table. All the data in the column will be lost.
  - You are about to drop the column `organizationSize` on the `Business` table. All the data in the column will be lost.
  - You are about to drop the column `referralSource` on the `Business` table. All the data in the column will be lost.
  - You are about to drop the column `stateHeadquarter` on the `Business` table. All the data in the column will be lost.

*/
-- AlterTable
ALTER TABLE "Business" DROP COLUMN "currency",
DROP COLUMN "department",
DROP COLUMN "industry",
DROP COLUMN "jobTitle",
DROP COLUMN "organizationSize",
DROP COLUMN "referralSource",
DROP COLUMN "stateHeadquarter",
ADD COLUMN     "currencyId" INTEGER,
ADD COLUMN     "departmentId" INTEGER,
ADD COLUMN     "discoverySourceId" INTEGER,
ADD COLUMN     "industryId" INTEGER,
ADD COLUMN     "jobTitleId" INTEGER,
ADD COLUMN     "organizationSizeId" INTEGER,
ADD COLUMN     "stateHeadquarterId" INTEGER;

-- CreateTable
CREATE TABLE "JobCategory" (
    "id" SERIAL NOT NULL,
    "name" TEXT NOT NULL,
    "order" INTEGER NOT NULL,
    "isActive" BOOLEAN NOT NULL DEFAULT true,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "JobCategory_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "JobTitle" (
    "jobTitleId" SERIAL NOT NULL,
    "name" TEXT NOT NULL,
    "order" INTEGER,
    "isCustom" BOOLEAN NOT NULL DEFAULT false,
    "isActive" BOOLEAN NOT NULL DEFAULT true,
    "categoryId" INTEGER NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "JobTitle_pkey" PRIMARY KEY ("jobTitleId")
);

-- CreateTable
CREATE TABLE "Department" (
    "departmentId" SERIAL NOT NULL,
    "name" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "Department_pkey" PRIMARY KEY ("departmentId")
);

-- CreateTable
CREATE TABLE "DiscoverySource" (
    "discoverySourceId" SERIAL NOT NULL,
    "name" TEXT NOT NULL,
    "description" TEXT,
    "isCustom" BOOLEAN NOT NULL DEFAULT false,
    "isActive" BOOLEAN NOT NULL DEFAULT true,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "DiscoverySource_pkey" PRIMARY KEY ("discoverySourceId")
);

-- CreateTable
CREATE TABLE "StateHeadquarter" (
    "stateHeadquarterId" SERIAL NOT NULL,
    "name" TEXT NOT NULL,
    "countryId" INTEGER NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "StateHeadquarter_pkey" PRIMARY KEY ("stateHeadquarterId")
);

-- CreateTable
CREATE TABLE "OrganizationSize" (
    "organizationSizeId" SERIAL NOT NULL,
    "minSize" INTEGER NOT NULL,
    "maxSize" INTEGER,
    "label" TEXT NOT NULL,
    "order" INTEGER,
    "isActive" BOOLEAN NOT NULL DEFAULT true,

    CONSTRAINT "OrganizationSize_pkey" PRIMARY KEY ("organizationSizeId")
);

-- CreateIndex
CREATE UNIQUE INDEX "DiscoverySource_name_key" ON "DiscoverySource"("name");

-- AddForeignKey
ALTER TABLE "Business" ADD CONSTRAINT "Business_discoverySourceId_fkey" FOREIGN KEY ("discoverySourceId") REFERENCES "DiscoverySource"("discoverySourceId") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Business" ADD CONSTRAINT "Business_industryId_fkey" FOREIGN KEY ("industryId") REFERENCES "Industry"("industryId") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Business" ADD CONSTRAINT "Business_headquarterId_fkey" FOREIGN KEY ("headquarterId") REFERENCES "Country"("countryId") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Business" ADD CONSTRAINT "Business_stateHeadquarterId_fkey" FOREIGN KEY ("stateHeadquarterId") REFERENCES "StateHeadquarter"("stateHeadquarterId") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Business" ADD CONSTRAINT "Business_organizationSizeId_fkey" FOREIGN KEY ("organizationSizeId") REFERENCES "OrganizationSize"("organizationSizeId") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Business" ADD CONSTRAINT "Business_departmentId_fkey" FOREIGN KEY ("departmentId") REFERENCES "Department"("departmentId") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Business" ADD CONSTRAINT "Business_currencyId_fkey" FOREIGN KEY ("currencyId") REFERENCES "Currency"("currencyId") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Business" ADD CONSTRAINT "Business_jobTitleId_fkey" FOREIGN KEY ("jobTitleId") REFERENCES "JobTitle"("jobTitleId") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "JobTitle" ADD CONSTRAINT "JobTitle_categoryId_fkey" FOREIGN KEY ("categoryId") REFERENCES "JobCategory"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "StateHeadquarter" ADD CONSTRAINT "StateHeadquarter_countryId_fkey" FOREIGN KEY ("countryId") REFERENCES "Country"("countryId") ON DELETE RESTRICT ON UPDATE CASCADE;
