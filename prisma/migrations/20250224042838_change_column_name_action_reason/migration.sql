/*
  Warnings:

  - You are about to drop the column `JobActionReasonid` on the `Job` table. All the data in the column will be lost.

*/
-- DropForeignKey
ALTER TABLE "Job" DROP CONSTRAINT "Job_JobActionReasonid_fkey";

-- AlterTable
ALTER TABLE "Job" DROP COLUMN "JobActionReasonid",
ADD COLUMN     "jobActionReasonid" INTEGER;

-- AddForeignKey
ALTER TABLE "Job" ADD CONSTRAINT "Job_jobActionReasonid_fkey" FOREIGN KEY ("jobActionReasonid") REFERENCES "JobActionReason"("id") ON DELETE SET NULL ON UPDATE CASCADE;
