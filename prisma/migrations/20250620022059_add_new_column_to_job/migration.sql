-- AlterTable
ALTER TABLE "Job" ADD COLUMN     "jobLocationId" INTEGER;

-- CreateTable
CREATE TABLE "JobLocation" (
    "jobLocationId" SERIAL NOT NULL,
    "countryId" INTEGER,
    "cityId" INTEGER,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "JobLocation_pkey" PRIMARY KEY ("jobLocationId")
);

-- AddForeignKey
ALTER TABLE "Job" ADD CONSTRAINT "Job_jobLocationId_fkey" FOREIGN KEY ("jobLocationId") REFERENCES "JobLocation"("jobLocationId") ON DELETE SET NULL ON UPDATE CASCADE;
