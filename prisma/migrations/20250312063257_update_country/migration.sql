/*
  Warnings:

  - You are about to drop the column `city` on the `Country` table. All the data in the column will be lost.
  - Made the column `language` on table `JobDescription` required. This step will fail if there are existing NULL values in that column.

*/
-- AlterTable
ALTER TABLE "Country" DROP COLUMN "city";

-- AlterTable
ALTER TABLE "JobDescription" ALTER COLUMN "language" SET NOT NULL;

-- CreateTable
CREATE TABLE "City" (
    "cityId" SERIAL NOT NULL,
    "countryId" INTEGER NOT NULL,
    "name" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "City_pkey" PRIMARY KEY ("cityId")
);

-- AddForeignKey
ALTER TABLE "City" ADD CONSTRAINT "City_countryId_fkey" FOREIGN KEY ("countryId") REFERENCES "Country"("countryId") ON DELETE RESTRICT ON UPDATE CASCADE;
