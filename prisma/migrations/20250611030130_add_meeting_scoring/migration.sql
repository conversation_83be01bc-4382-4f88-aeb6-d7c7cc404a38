-- CreateTable
CREATE TABLE "MeetingTranscription" (
    "meetingTranscriptionId" SERIAL NOT NULL,
    "role" TEXT,
    "content" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "aIBotTestRoomId" INTEGER NOT NULL,

    CONSTRAINT "MeetingTranscription_pkey" PRIMARY KEY ("meetingTranscriptionId")
);

-- CreateTable
CREATE TABLE "MeetingScoring" (
    "meetingScoringId" SERIAL NOT NULL,
    "fluencyAndCoherence" TEXT,
    "lexicalResource" TEXT,
    "grammaticalRangeAndAccuracy" TEXT,
    "CEFRStandard" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "aIBotTestRoomId" INTEGER NOT NULL,

    CONSTRAINT "MeetingScoring_pkey" PRIMARY KEY ("meetingScoringId")
);

-- CreateTable
CREATE TABLE "MeetingFeedback" (
    "meetingFeedbackId" SERIAL NOT NULL,
    "aiAssessment" TEXT,
    "scoringLevel" TEXT,
    "question" TEXT,
    "idealAnswer" TEXT,
    "userAnswer" TEXT,
    "meetingScoringId" INTEGER NOT NULL,

    CONSTRAINT "MeetingFeedback_pkey" PRIMARY KEY ("meetingFeedbackId")
);

-- CreateIndex
CREATE UNIQUE INDEX "MeetingScoring_aIBotTestRoomId_key" ON "MeetingScoring"("aIBotTestRoomId");

-- AddForeignKey
ALTER TABLE "MeetingTranscription" ADD CONSTRAINT "MeetingTranscription_aIBotTestRoomId_fkey" FOREIGN KEY ("aIBotTestRoomId") REFERENCES "AIBotTestRoom"("aIBotTestRoomId") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "MeetingScoring" ADD CONSTRAINT "MeetingScoring_aIBotTestRoomId_fkey" FOREIGN KEY ("aIBotTestRoomId") REFERENCES "AIBotTestRoom"("aIBotTestRoomId") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "MeetingFeedback" ADD CONSTRAINT "MeetingFeedback_meetingScoringId_fkey" FOREIGN KEY ("meetingScoringId") REFERENCES "MeetingScoring"("meetingScoringId") ON DELETE RESTRICT ON UPDATE CASCADE;
