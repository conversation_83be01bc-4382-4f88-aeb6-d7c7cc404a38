-- CreateEnum
CREATE TYPE "AIBotTestRecordingStatus" AS ENUM ('RECORDING', 'COMPLETED');

-- CreateEnum
CREATE TYPE "AIBotTestRoomStatus" AS ENUM ('PENDING', 'COMPLETED');

-- AlterTable
ALTER TABLE "AIBotTest" ADD COLUMN     "numberOfTrial" INTEGER NOT NULL DEFAULT 0,
ALTER COLUMN "score" DROP NOT NULL,
ALTER COLUMN "status" DROP NOT NULL;

-- CreateTable
CREATE TABLE "AIBotTestRoom" (
    "aIBotTestRoomId" SERIAL NOT NULL,
    "aiBotTestSectionId" INTEGER,
    "aiBotTestId" INTEGER,
    "roomName" TEXT,
    "roomUrl" TEXT,
    "roomNumber" INTEGER,
    "roomExpireTime" TIMESTAMP(3),
    "roomStatus" "AIBotTestRecordingStatus",
    "roomToken" TEXT,
    "duration" INTEGER,
    "recordingUrl" TEXT,
    "recordingStatus" "AIBotTestRecordingStatus",
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "AIBotTestRoom_pkey" PRIMARY KEY ("aIBotTestRoomId")
);

-- CreateIndex
CREATE UNIQUE INDEX "AIBotTestRoom_roomName_key" ON "AIBotTestRoom"("roomName");

-- AddForeignKey
ALTER TABLE "AIBotTestRoom" ADD CONSTRAINT "AIBotTestRoom_aiBotTestId_fkey" FOREIGN KEY ("aiBotTestId") REFERENCES "AIBotTest"("aiBotTestId") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "AIBotTestRoom" ADD CONSTRAINT "AIBotTestRoom_aiBotTestSectionId_fkey" FOREIGN KEY ("aiBotTestSectionId") REFERENCES "AIBotTestSection"("aiBotTestSectionId") ON DELETE SET NULL ON UPDATE CASCADE;
