/*
  Warnings:

  - A unique constraint covering the columns `[countryId]` on the table `EmploymentHistory` will be added. If there are existing duplicate values, this will fail.
  - A unique constraint covering the columns `[cityId]` on the table `EmploymentHistory` will be added. If there are existing duplicate values, this will fail.

*/
-- AlterTable
ALTER TABLE "EmploymentHistory" ADD COLUMN     "cityId" INTEGER,
ADD COLUMN     "countryId" INTEGER;

-- CreateIndex
CREATE UNIQUE INDEX "EmploymentHistory_countryId_key" ON "EmploymentHistory"("countryId");

-- CreateIndex
CREATE UNIQUE INDEX "EmploymentHistory_cityId_key" ON "EmploymentHistory"("cityId");

-- AddForeignKey
ALTER TABLE "EmploymentHistory" ADD CONSTRAINT "EmploymentHistory_countryId_fkey" FOREIGN KEY ("countryId") REFERENCES "Country"("countryId") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddF<PERSON>ign<PERSON>ey
ALTER TABLE "EmploymentHistory" ADD CONSTRAINT "EmploymentHistory_cityId_fkey" FOREIGN KEY ("cityId") REFERENCES "City"("cityId") ON DELETE SET NULL ON UPDATE CASCADE;
