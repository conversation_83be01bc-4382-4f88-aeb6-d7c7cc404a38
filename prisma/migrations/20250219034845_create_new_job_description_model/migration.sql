-- AlterTable
ALTER TABLE "Job" ADD COLUMN     "createdBy" TEXT,
ADD COLUMN     "jobDescriptionId" INTEGER;

-- CreateTable
CREATE TABLE "JobDescription" (
    "id" SERIAL NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "createdBy" TEXT,
    "companyOverview" TEXT NOT NULL,
    "commitment" TEXT NOT NULL,
    "level" TEXT NOT NULL,
    "jobTitle" TEXT NOT NULL,
    "yearsExperience" TEXT NOT NULL,
    "skillsRequired" TEXT NOT NULL,
    "roleExpectation" TEXT NOT NULL,
    "mustHave" TEXT NOT NULL,
    "niceToHave" TEXT NOT NULL,

    CONSTRAINT "JobDescription_pkey" PRIMARY KEY ("id")
);
