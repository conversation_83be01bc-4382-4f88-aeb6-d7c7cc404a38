-- AlterEnum
-- This migration adds more than one value to an enum.
-- With PostgreSQL versions 11 and earlier, this is not possible
-- in a single migration. This can be worked around by creating
-- multiple migrations, each migration adding only one value to
-- the enum.


ALTER TYPE "InternalApplicationStatus" ADD VALUE 'HUMAN_INTERVIEW';
ALTER TYPE "InternalApplicationStatus" ADD VALUE 'WITHDREW_B4_SL';
ALTER TYPE "InternalApplicationStatus" ADD VALUE 'FAILED';
ALTER TYPE "InternalApplicationStatus" ADD VALUE 'CONTRACTING';
ALTER TYPE "InternalApplicationStatus" ADD VALUE 'WITHDREW';
