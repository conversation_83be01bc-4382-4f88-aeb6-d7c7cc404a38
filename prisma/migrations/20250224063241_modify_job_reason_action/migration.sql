/*
  Warnings:

  - You are about to drop the column `jobActionReasonid` on the `Job` table. All the data in the column will be lost.
  - A unique constraint covering the columns `[jobActionReasonId]` on the table `Job` will be added. If there are existing duplicate values, this will fail.

*/
-- DropForeignKey
ALTER TABLE "Job" DROP CONSTRAINT "Job_jobActionReasonid_fkey";

-- AlterTable
ALTER TABLE "Job" DROP COLUMN "jobActionReasonid",
ADD COLUMN     "jobActionReasonId" INTEGER;

-- CreateIndex
CREATE UNIQUE INDEX "Job_jobActionReasonId_key" ON "Job"("jobActionReasonId");

-- AddForeignKey
ALTER TABLE "Job" ADD CONSTRAINT "Job_jobActionReasonId_fkey" FOREIGN KEY ("jobActionReasonId") REFERENCES "JobActionReason"("id") ON DELETE SET NULL ON UPDATE CASCADE;
