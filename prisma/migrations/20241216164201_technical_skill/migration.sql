/*
  Warnings:

  - You are about to drop the column `profileId` on the `TechnicalSkill` table. All the data in the column will be lost.

*/
-- DropForeignKey
ALTER TABLE "Profile" DROP CONSTRAINT "Profile_technicalSkillId_fkey";

-- AlterTable
ALTER TABLE "TechnicalSkill" DROP COLUMN "profileId";

-- CreateTable
CREATE TABLE "UserTechnicalSkill" (
    "userTechnicalSkillId" SERIAL NOT NULL,
    "profileId" INTEGER NOT NULL,
    "technicalSkillId" INTEGER,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "UserTechnicalSkill_pkey" PRIMARY KEY ("userTechnicalSkillId")
);

-- AddForeignKey
ALTER TABLE "UserTechnicalSkill" ADD CONSTRAINT "UserTechnicalSkill_technicalSkillId_fkey" FOREIGN KEY ("technicalSkillId") REFERENCES "TechnicalSkill"("technicalSkillId") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddF<PERSON>ignKey
ALTER TABLE "UserTechnicalSkill" ADD CONSTRAINT "UserTechnicalSkill_profileId_fkey" FOREIGN KEY ("profileId") REFERENCES "Profile"("profileId") ON DELETE RESTRICT ON UPDATE CASCADE;
