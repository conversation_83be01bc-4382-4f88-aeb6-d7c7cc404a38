-- DropF<PERSON>ign<PERSON>ey
ALTER TABLE "Channel" DROP CONSTRAINT "Channel_platformId_fkey";

-- AlterTable
ALTER TABLE "Award" ALTER COLUMN "title" DROP NOT NULL,
ALTER COLUMN "date" DROP NOT NULL;

-- AlterTable
ALTER TABLE "Certification" ALTER COLUMN "name" DROP NOT NULL;

-- AlterTable
ALTER TABLE "Channel" ALTER COLUMN "profileId" DROP NOT NULL,
ALTER COLUMN "type" DROP NOT NULL,
ALTER COLUMN "platformId" DROP NOT NULL,
ALTER COLUMN "platformUrl" DROP NOT NULL;

-- AlterTable
ALTER TABLE "Publication" ALTER COLUMN "title" DROP NOT NULL,
ALTER COLUMN "publicationName" DROP NOT NULL,
ALTER COLUMN "dateOfPublication" DROP NOT NULL;

-- AlterTable
ALTER TABLE "UserCertification" ALTER COLUMN "score" DROP NOT NULL,
ALTER COLUMN "issuer" DROP NOT NULL,
ALTER COLUMN "issueDate" DROP NOT NULL;

-- AddForeignKey
ALTER TABLE "Channel" ADD CONSTRAINT "Channel_platformId_fkey" FOREIGN KEY ("platformId") REFERENCES "PlatformChannel"("platformId") ON DELETE SET NULL ON UPDATE CASCADE;
