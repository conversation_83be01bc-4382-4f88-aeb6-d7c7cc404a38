-- DropForeign<PERSON>ey
ALTER TABLE "WorkPreference" DROP CONSTRAINT "WorkPreference_countryId_fkey";

-- CreateTable
CREATE TABLE "UserCountry" (
    "userCountryId" SERIAL NOT NULL,
    "workPreferenceId" INTEGER,
    "countryId" INTEGER NOT NULL,
    "cityId" INTEGER,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "UserCountry_pkey" PRIMARY KEY ("userCountryId")
);

-- CreateIndex
CREATE UNIQUE INDEX "UserCountry_workPreferenceId_key" ON "UserCountry"("workPreferenceId");

-- AddForeignKey
ALTER TABLE "UserCountry" ADD CONSTRAINT "UserCountry_workPreferenceId_fkey" FOREIGN KEY ("workPreferenceId") REFERENCES "WorkPreference"("workPreferenceId") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddF<PERSON>ign<PERSON>ey
ALTER TABLE "UserCountry" ADD CONSTRAINT "UserCountry_countryId_fkey" FOREIGN KEY ("countryId") REFERENCES "Country"("countryId") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "UserCountry" ADD CONSTRAINT "UserCountry_cityId_fkey" FOREIGN KEY ("cityId") REFERENCES "City"("cityId") ON DELETE SET NULL ON UPDATE CASCADE;
