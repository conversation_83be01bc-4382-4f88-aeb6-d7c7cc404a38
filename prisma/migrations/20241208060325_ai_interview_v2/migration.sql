/*
  Warnings:

  - A unique constraint covering the columns `[workPreferenceId]` on the table `Profile` will be added. If there are existing duplicate values, this will fail.

*/
-- CreateEnum
CREATE TYPE "ReferralSource" AS ENUM ('Facebook', 'Linkedin', 'Google', 'Other');

-- CreateEnum
CREATE TYPE "MasterFormStatus" AS ENUM ('PENDING', 'COMPLETED');

-- CreateEnum
CREATE TYPE "WorkAvailability" AS ENUM ('FullTime', 'HalfTime', 'Flexible');

-- CreateEnum
CREATE TYPE "WorkStartDay" AS ENUM ('Immediately', 'AfterSpecificDate', 'AfterOffer');

-- CreateEnum
CREATE TYPE "ChannelType" AS ENUM ('SOCIAL_MEDIA', 'CODING', 'PORTFOLIO');

-- AlterTable
ALTER TABLE "AcademyHistory" ADD COLUMN     "degreeStatus" TEXT,
ADD COLUMN     "gpa" DOUBLE PRECISION;

-- AlterTable
ALTER TABLE "Certification" ADD COLUMN     "issueDate" TIMESTAMP(3),
ADD COLUMN     "proof" TEXT;

-- AlterTable
ALTER TABLE "CodingQuestion" ADD COLUMN     "codingTestSectionId" INTEGER;

-- AlterTable
ALTER TABLE "EmploymentHistory" ADD COLUMN     "description" TEXT[];

-- AlterTable
ALTER TABLE "Profile" ADD COLUMN     "referralSource" "ReferralSource",
ADD COLUMN     "technicalSkillId" INTEGER,
ADD COLUMN     "workPreferenceId" INTEGER;

-- CreateTable
CREATE TABLE "WorkPreference" (
    "workPreferenceId" SERIAL NOT NULL,
    "countryId" INTEGER,
    "profileId" INTEGER,
    "workAvailability" "WorkAvailability" NOT NULL,
    "currentSalary" DOUBLE PRECISION NOT NULL,
    "idealSalary" DOUBLE PRECISION NOT NULL,
    "minimumSalary" DOUBLE PRECISION NOT NULL,
    "startDateOption" "WorkStartDay" NOT NULL,
    "startDate" TIMESTAMP(3),
    "startAfterOfferDays" INTEGER,
    "citizenship" TEXT[],
    "taxResidence" TEXT NOT NULL,
    "authorizedToWork" BOOLEAN NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "WorkPreference_pkey" PRIMARY KEY ("workPreferenceId")
);

-- CreateTable
CREATE TABLE "MasterForm" (
    "masterFormEmail" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "status" "MasterFormStatus" NOT NULL,

    CONSTRAINT "MasterForm_pkey" PRIMARY KEY ("masterFormEmail")
);

-- CreateTable
CREATE TABLE "ResumeUploadSection" (
    "resumeUploadSectionId" SERIAL NOT NULL,
    "masterFormEmail" TEXT,
    "resumeUrl" TEXT NOT NULL,
    "resumeContent" JSONB,
    "status" "MasterFormStatus" NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "ResumeUploadSection_pkey" PRIMARY KEY ("resumeUploadSectionId")
);

-- CreateTable
CREATE TABLE "SkillInterviewSelected" (
    "skillInterviewSelectedId" SERIAL NOT NULL,
    "skillSelectionSectionId" INTEGER NOT NULL,
    "technicalSkillId" INTEGER NOT NULL,
    "skillLevelId" INTEGER NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "SkillInterviewSelected_pkey" PRIMARY KEY ("skillInterviewSelectedId")
);

-- CreateTable
CREATE TABLE "SkillSelectionSection" (
    "skillSelectionSectionId" SERIAL NOT NULL,
    "masterFormEmail" TEXT,
    "status" "MasterFormStatus" NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "SkillSelectionSection_pkey" PRIMARY KEY ("skillSelectionSectionId")
);

-- CreateTable
CREATE TABLE "CodingTestSection" (
    "codingTestSectionId" SERIAL NOT NULL,
    "masterFormEmail" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "score" INTEGER NOT NULL,
    "screenRecordUrl" TEXT,
    "webcamRecordUrl" TEXT,
    "status" "TestStatus" NOT NULL,

    CONSTRAINT "CodingTestSection_pkey" PRIMARY KEY ("codingTestSectionId")
);

-- CreateTable
CREATE TABLE "AIBotTestSection" (
    "aiBotTestSectionId" SERIAL NOT NULL,
    "masterFormEmail" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "score" INTEGER NOT NULL,
    "status" "TestStatus" NOT NULL,
    "conversationId" TEXT,
    "screenRecordUrl" TEXT,
    "audioRecordUrl" TEXT,

    CONSTRAINT "AIBotTestSection_pkey" PRIMARY KEY ("aiBotTestSectionId")
);

-- CreateTable
CREATE TABLE "PersonalProject" (
    "personalProjectId" SERIAL NOT NULL,
    "profileId" INTEGER NOT NULL,
    "projectName" TEXT NOT NULL,
    "startDate" TIMESTAMP(3),
    "endDate" TIMESTAMP(3),
    "workingPosition" TEXT,
    "description" TEXT[],
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "PersonalProject_pkey" PRIMARY KEY ("personalProjectId")
);

-- CreateTable
CREATE TABLE "Publication" (
    "id" SERIAL NOT NULL,
    "profileId" INTEGER NOT NULL,
    "title" TEXT NOT NULL,
    "publicationName" TEXT NOT NULL,
    "dateOfPublication" TIMESTAMP(3) NOT NULL,
    "link" TEXT,
    "authors" TEXT[],
    "description" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "Publication_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "Award" (
    "id" SERIAL NOT NULL,
    "profileId" INTEGER NOT NULL,
    "title" TEXT NOT NULL,
    "date" TIMESTAMP(3) NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "Award_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "Channel" (
    "channelId" SERIAL NOT NULL,
    "profileId" INTEGER NOT NULL,
    "type" "ChannelType" NOT NULL,
    "platformId" INTEGER NOT NULL,
    "platformUrl" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "Channel_pkey" PRIMARY KEY ("channelId")
);

-- CreateTable
CREATE TABLE "PlatformChannel" (
    "platformId" SERIAL NOT NULL,
    "platFormName" TEXT NOT NULL,
    "logoUrl" TEXT,
    "description" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "PlatformChannel_pkey" PRIMARY KEY ("platformId")
);

-- CreateTable
CREATE TABLE "TechnicalSkill" (
    "technicalSkillId" SERIAL NOT NULL,
    "profileId" INTEGER NOT NULL,
    "skillName" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "TechnicalSkill_pkey" PRIMARY KEY ("technicalSkillId")
);

-- CreateIndex
CREATE UNIQUE INDEX "WorkPreference_countryId_key" ON "WorkPreference"("countryId");

-- CreateIndex
CREATE UNIQUE INDEX "WorkPreference_profileId_key" ON "WorkPreference"("profileId");

-- CreateIndex
CREATE UNIQUE INDEX "ResumeUploadSection_masterFormEmail_key" ON "ResumeUploadSection"("masterFormEmail");

-- CreateIndex
CREATE UNIQUE INDEX "SkillSelectionSection_masterFormEmail_key" ON "SkillSelectionSection"("masterFormEmail");

-- CreateIndex
CREATE UNIQUE INDEX "CodingTestSection_masterFormEmail_key" ON "CodingTestSection"("masterFormEmail");

-- CreateIndex
CREATE UNIQUE INDEX "AIBotTestSection_masterFormEmail_key" ON "AIBotTestSection"("masterFormEmail");

-- CreateIndex
CREATE UNIQUE INDEX "Profile_workPreferenceId_key" ON "Profile"("workPreferenceId");

-- AddForeignKey
ALTER TABLE "Profile" ADD CONSTRAINT "Profile_technicalSkillId_fkey" FOREIGN KEY ("technicalSkillId") REFERENCES "TechnicalSkill"("technicalSkillId") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Profile" ADD CONSTRAINT "Profile_workPreferenceId_fkey" FOREIGN KEY ("workPreferenceId") REFERENCES "WorkPreference"("workPreferenceId") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "WorkPreference" ADD CONSTRAINT "WorkPreference_countryId_fkey" FOREIGN KEY ("countryId") REFERENCES "Country"("countryId") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ResumeUploadSection" ADD CONSTRAINT "ResumeUploadSection_masterFormEmail_fkey" FOREIGN KEY ("masterFormEmail") REFERENCES "MasterForm"("masterFormEmail") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "SkillInterviewSelected" ADD CONSTRAINT "SkillInterviewSelected_skillLevelId_fkey" FOREIGN KEY ("skillLevelId") REFERENCES "SkillLevel"("skillLevelId") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "SkillInterviewSelected" ADD CONSTRAINT "SkillInterviewSelected_technicalSkillId_fkey" FOREIGN KEY ("technicalSkillId") REFERENCES "TechnicalSkill"("technicalSkillId") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "SkillInterviewSelected" ADD CONSTRAINT "SkillInterviewSelected_skillSelectionSectionId_fkey" FOREIGN KEY ("skillSelectionSectionId") REFERENCES "SkillSelectionSection"("skillSelectionSectionId") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "SkillSelectionSection" ADD CONSTRAINT "SkillSelectionSection_masterFormEmail_fkey" FOREIGN KEY ("masterFormEmail") REFERENCES "MasterForm"("masterFormEmail") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "CodingTestSection" ADD CONSTRAINT "CodingTestSection_masterFormEmail_fkey" FOREIGN KEY ("masterFormEmail") REFERENCES "MasterForm"("masterFormEmail") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "AIBotTestSection" ADD CONSTRAINT "AIBotTestSection_masterFormEmail_fkey" FOREIGN KEY ("masterFormEmail") REFERENCES "MasterForm"("masterFormEmail") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "CodingQuestion" ADD CONSTRAINT "CodingQuestion_codingTestSectionId_fkey" FOREIGN KEY ("codingTestSectionId") REFERENCES "CodingTestSection"("codingTestSectionId") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "PersonalProject" ADD CONSTRAINT "PersonalProject_profileId_fkey" FOREIGN KEY ("profileId") REFERENCES "Profile"("profileId") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Publication" ADD CONSTRAINT "Publication_profileId_fkey" FOREIGN KEY ("profileId") REFERENCES "Profile"("profileId") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Award" ADD CONSTRAINT "Award_profileId_fkey" FOREIGN KEY ("profileId") REFERENCES "Profile"("profileId") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Channel" ADD CONSTRAINT "Channel_profileId_fkey" FOREIGN KEY ("profileId") REFERENCES "Profile"("profileId") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Channel" ADD CONSTRAINT "Channel_platformId_fkey" FOREIGN KEY ("platformId") REFERENCES "PlatformChannel"("platformId") ON DELETE RESTRICT ON UPDATE CASCADE;
