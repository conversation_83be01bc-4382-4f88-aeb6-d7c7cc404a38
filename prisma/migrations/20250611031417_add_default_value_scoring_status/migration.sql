/*
  Warnings:

  - The values [SUCCESS] on the enum `HandleScoringStatus` will be removed. If these variants are still used in the database, this will fail.

*/
-- AlterEnum
BEGIN;
CREATE TYPE "HandleScoringStatus_new" AS ENUM ('PROCESSING', 'FAILED', 'DONE', 'PENDING');
ALTER TABLE "AIBotTestRoom" ALTER COLUMN "handleScoringStatus" TYPE "HandleScoringStatus_new" USING ("handleScoringStatus"::text::"HandleScoringStatus_new");
ALTER TYPE "HandleScoringStatus" RENAME TO "HandleScoringStatus_old";
ALTER TYPE "HandleScoringStatus_new" RENAME TO "HandleScoringStatus";
DROP TYPE "HandleScoringStatus_old";
COMMIT;

-- AlterTable
ALTER TABLE "AIBotTestRoom" ALTER COLUMN "handleScoringStatus" SET DEFAULT 'PENDING';
