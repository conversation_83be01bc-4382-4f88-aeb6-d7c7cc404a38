-- AlterTable
ALTER TABLE "Application" ADD COLUMN     "remark" TEXT;

-- CreateTable
CREATE TABLE "Certification" (
    "certificationId" SERIAL NOT NULL,
    "name" TEXT NOT NULL,
    "description" TEXT,
    "type" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "Certification_pkey" PRIMARY KEY ("certificationId")
);

-- CreateTable
CREATE TABLE "UserCertification" (
    "userId" INTEGER NOT NULL,
    "roleId" INTEGER NOT NULL,
    "certificationId" INTEGER NOT NULL,
    "score" TEXT NOT NULL,
    "issuer" TEXT NOT NULL,
    "attachmentUrl" TEXT,
    "issueDate" TIMESTAMP(3) NOT NULL,
    "expirationDate" TIMESTAMP(3),
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "UserCertification_pkey" PRIMARY KEY ("userId","roleId","certificationId")
);

-- AddForeignKey
ALTER TABLE "UserCertification" ADD CONSTRAINT "UserCertification_certificationId_fkey" FOREIGN KEY ("certificationId") REFERENCES "Certification"("certificationId") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "UserCertification" ADD CONSTRAINT "UserCertification_userId_roleId_fkey" FOREIGN KEY ("userId", "roleId") REFERENCES "UserRole"("userId", "roleId") ON DELETE RESTRICT ON UPDATE CASCADE;
