-- CreateTable
CREATE TABLE "EnglishTestSection" (
    "englishTestSectionId" SERIAL NOT NULL,
    "masterFormEmail" TEXT,
    "question" TEXT NOT NULL,
    "userAnswer" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "score" INTEGER NOT NULL,
    "status" "TestStatus" NOT NULL,
    "screenRecordUrl" TEXT,
    "webcamRecordUrl" TEXT,

    CONSTRAINT "EnglishTestSection_pkey" PRIMARY KEY ("englishTestSectionId")
);

-- CreateIndex
CREATE UNIQUE INDEX "EnglishTestSection_masterFormEmail_key" ON "EnglishTestSection"("masterFormEmail");

-- AddForeignKey
ALTER TABLE "EnglishTestSection" ADD CONSTRAINT "EnglishTestSection_masterFormEmail_fkey" FOREIGN KEY ("masterFormEmail") REFERENCES "MasterForm"("masterFormEmail") ON DELETE SET NULL ON UPDATE CASCADE;
