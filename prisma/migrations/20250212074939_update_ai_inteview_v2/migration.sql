/*
  Warnings:

  - You are about to drop the column `degreeStatus` on the `AcademyHistory` table. All the data in the column will be lost.
  - You are about to drop the column `proficiency` on the `UserLanguage` table. All the data in the column will be lost.

*/
-- AlterTable
ALTER TABLE "AcademyHistory" DROP COLUMN "degreeStatus",
ADD COLUMN     "currentlyPursuing" BOOLEAN,
ADD COLUMN     "degreeStatusId" INTEGER;

-- AlterTable
ALTER TABLE "PersonalProject" ADD COLUMN     "currentlyDoing" BOOLEAN;

-- AlterTable
ALTER TABLE "UserLanguage" DROP COLUMN "proficiency",
ADD COLUMN     "languageProficiencyId" INTEGER;

-- CreateTable
CREATE TABLE "LanguageProficiency" (
    "languageProficiencyId" SERIAL NOT NULL,
    "languageProficiencyName" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "LanguageProficiency_pkey" PRIMARY KEY ("languageProficiencyId")
);

-- CreateTable
CREATE TABLE "DegreeStatus" (
    "degreeStatusId" SERIAL NOT NULL,
    "degreeStatusName" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "DegreeStatus_pkey" PRIMARY KEY ("degreeStatusId")
);

-- CreateTable
CREATE TABLE "EmploymentTechnicalSkill" (
    "employmentTechnicalSkillId" SERIAL NOT NULL,
    "employmentHistoryId" INTEGER NOT NULL,
    "technicalSkillId" INTEGER,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "EmploymentTechnicalSkill_pkey" PRIMARY KEY ("employmentTechnicalSkillId")
);

-- AddForeignKey
ALTER TABLE "UserLanguage" ADD CONSTRAINT "UserLanguage_languageProficiencyId_fkey" FOREIGN KEY ("languageProficiencyId") REFERENCES "LanguageProficiency"("languageProficiencyId") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "AcademyHistory" ADD CONSTRAINT "AcademyHistory_degreeStatusId_fkey" FOREIGN KEY ("degreeStatusId") REFERENCES "DegreeStatus"("degreeStatusId") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "EmploymentTechnicalSkill" ADD CONSTRAINT "EmploymentTechnicalSkill_technicalSkillId_fkey" FOREIGN KEY ("technicalSkillId") REFERENCES "TechnicalSkill"("technicalSkillId") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "EmploymentTechnicalSkill" ADD CONSTRAINT "EmploymentTechnicalSkill_employmentHistoryId_fkey" FOREIGN KEY ("employmentHistoryId") REFERENCES "EmploymentHistory"("employmentHistoryId") ON DELETE RESTRICT ON UPDATE CASCADE;
