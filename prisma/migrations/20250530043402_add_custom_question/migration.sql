-- CreateTable
CREATE TABLE "CustomQuestion" (
    "customQuestionId" TEXT NOT NULL,
    "interviewGroupId" TEXT NOT NULL,
    "question" VARCHAR(500) NOT NULL,
    "answer" VARCHAR(1000),
    "order" INTEGER NOT NULL DEFAULT 0,
    "isRequired" BOOLEAN NOT NULL DEFAULT true,
    "duration" INTEGER DEFAULT 120,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "CustomQuestion_pkey" PRIMARY KEY ("customQuestionId")
);

-- CreateIndex
CREATE INDEX "CustomQuestion_interviewGroupId_idx" ON "CustomQuestion"("interviewGroupId");

-- Add<PERSON>oreignKey
ALTER TABLE "CustomQuestion" ADD CONSTRAINT "CustomQuestion_interviewGroupId_fkey" FOREIGN KEY ("interviewGroupId") REFERENCES "InterviewGroup"("interviewGroupId") ON DELETE CASCADE ON UPDATE CASCADE;
