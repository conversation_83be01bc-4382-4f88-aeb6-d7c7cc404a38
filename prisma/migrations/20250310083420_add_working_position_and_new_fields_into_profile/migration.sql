-- CreateEnum
CREATE TYPE "Seniority" AS ENUM ('INTERN', 'NEW_GRAD', 'JUNIOR', 'MID', 'SENIOR');

-- AlterTable
ALTER TABLE "Profile" ADD COLUMN     "profileManagedByAdminId" INTEGER,
ADD COLUMN     "seniority" "Seniority";

-- CreateTable
CREATE TABLE "ProfileManagedByAdmin" (
    "profileManagedByAdminId" SERIAL NOT NULL,
    "profileId" INTEGER NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "ProfileManagedByAdmin_pkey" PRIMARY KEY ("profileManagedByAdminId")
);

-- CreateTable
CREATE TABLE "ProfileTopTechnicalSkill" (
    "profileManagedByAdminId" INTEGER NOT NULL,
    "technicalSkillId" INTEGER NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "ProfileTopTechnicalSkill_pkey" PRIMARY KEY ("profileManagedByAdminId","technicalSkillId")
);

-- CreateTable
CREATE TABLE "WorkingPosition" (
    "workingPositionId" SERIAL NOT NULL,
    "position" TEXT NOT NULL,
    "description" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "WorkingPosition_pkey" PRIMARY KEY ("workingPositionId")
);

-- CreateTable
CREATE TABLE "ProfileWorkingPosition" (
    "profileManagedByAdminId" INTEGER NOT NULL,
    "workingPositionId" INTEGER NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "ProfileWorkingPosition_pkey" PRIMARY KEY ("profileManagedByAdminId","workingPositionId")
);

-- CreateIndex
CREATE UNIQUE INDEX "ProfileManagedByAdmin_profileId_key" ON "ProfileManagedByAdmin"("profileId");

-- CreateIndex
CREATE INDEX "ProfileManagedByAdmin_profileId_idx" ON "ProfileManagedByAdmin"("profileId");

-- CreateIndex
CREATE INDEX "ProfileTopTechnicalSkill_profileManagedByAdminId_idx" ON "ProfileTopTechnicalSkill"("profileManagedByAdminId");

-- CreateIndex
CREATE INDEX "ProfileWorkingPosition_profileManagedByAdminId_idx" ON "ProfileWorkingPosition"("profileManagedByAdminId");

-- AddForeignKey
ALTER TABLE "ProfileManagedByAdmin" ADD CONSTRAINT "ProfileManagedByAdmin_profileId_fkey" FOREIGN KEY ("profileId") REFERENCES "Profile"("profileId") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ProfileTopTechnicalSkill" ADD CONSTRAINT "ProfileTopTechnicalSkill_technicalSkillId_fkey" FOREIGN KEY ("technicalSkillId") REFERENCES "TechnicalSkill"("technicalSkillId") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ProfileTopTechnicalSkill" ADD CONSTRAINT "ProfileTopTechnicalSkill_profileManagedByAdminId_fkey" FOREIGN KEY ("profileManagedByAdminId") REFERENCES "ProfileManagedByAdmin"("profileManagedByAdminId") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ProfileWorkingPosition" ADD CONSTRAINT "ProfileWorkingPosition_workingPositionId_fkey" FOREIGN KEY ("workingPositionId") REFERENCES "WorkingPosition"("workingPositionId") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ProfileWorkingPosition" ADD CONSTRAINT "ProfileWorkingPosition_profileManagedByAdminId_fkey" FOREIGN KEY ("profileManagedByAdminId") REFERENCES "ProfileManagedByAdmin"("profileManagedByAdminId") ON DELETE RESTRICT ON UPDATE CASCADE;
