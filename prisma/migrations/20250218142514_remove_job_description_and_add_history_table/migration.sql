/*
  Warnings:

  - You are about to drop the `JobDescription` table. If the table is not empty, all the data it contains will be lost.
  - You are about to drop the `JobDescriptionHistory` table. If the table is not empty, all the data it contains will be lost.

*/
-- DropForeignKey
ALTER TABLE "JobDescriptionHistory" DROP CONSTRAINT "JobDescriptionHistory_job_description_id_fkey";

-- AlterTable
ALTER TABLE "Job" ADD COLUMN     "roleExpectation" TEXT;

-- DropTable
DROP TABLE "JobDescription";

-- DropTable
DROP TABLE "JobDescriptionHistory";

-- CreateTable
CREATE TABLE "JobHistory" (
    "id" TEXT NOT NULL,
    "jobId" INTEGER NOT NULL,
    "updatedBy" TEXT NOT NULL,
    "changes" JSONB NOT NULL,
    "updatedAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "JobHistory_pkey" PRIMARY KEY ("id")
);

-- AddForeignKey
ALTER TABLE "JobHistory" ADD CONSTRAINT "JobHistory_jobId_fkey" FOREIGN KEY ("jobId") REFERENCES "Job"("jobId") ON DELETE CASCADE ON UPDATE CASCADE;
