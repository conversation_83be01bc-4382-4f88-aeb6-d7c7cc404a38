-- CreateTable
CREATE TABLE "Business" (
    "businessId" SERIAL NOT NULL,
    "email" TEXT NOT NULL,
    "isVerified" BOOLEAN DEFAULT false,
    "verficationToken" TEXT,
    "firstName" TEXT,
    "middleName" TEXT,
    "lastName" TEXT,
    "preferredFirstName" TEXT,
    "countryCode" TEXT,
    "phoneNumber" TEXT,
    "jobTitle" TEXT,
    "referralSource" TEXT,
    "organizationName" TEXT,
    "organizationSize" TEXT,
    "headquarterId" INTEGER,
    "stateHeadquarter" TEXT,
    "department" TEXT,
    "companyWebsiteURL" TEXT,
    "companyLinkedInURL" TEXT,
    "userId" INTEGER,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "Business_pkey" PRIMARY KEY ("businessId")
);

-- CreateIndex
CREATE UNIQUE INDEX "Business_userId_key" ON "Business"("userId");

-- AddForeignKey
ALTER TABLE "Business" ADD CONSTRAINT "Business_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("userId") ON DELETE SET NULL ON UPDATE CASCADE;
