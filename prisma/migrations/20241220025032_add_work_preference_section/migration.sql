-- AlterTable
ALTER TABLE "AIBotTestQuestion" ADD COLUMN     "aiBotTestSectionId" INTEGER;

-- AlterTable
ALTER TABLE "AIBotTestSection" ADD COLUMN     "conversationName" TEXT;

-- CreateTable
CREATE TABLE "WorkPreferenceSection" (
    "workPreferenceSectionId" SERIAL NOT NULL,
    "masterFormEmail" TEXT,
    "workPreferenceContent" JSONB,
    "status" "MasterFormStatus" NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "WorkPreferenceSection_pkey" PRIMARY KEY ("workPreferenceSectionId")
);

-- CreateIndex
CREATE UNIQUE INDEX "WorkPreferenceSection_masterFormEmail_key" ON "WorkPreferenceSection"("masterFormEmail");

-- AddForeignKey
ALTER TABLE "WorkPreferenceSection" ADD CONSTRAINT "WorkPreferenceSection_masterFormEmail_fkey" FOREIGN KEY ("masterFormEmail") REFERENCES "MasterForm"("masterFormEmail") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "AIBotTestQuestion" ADD CONSTRAINT "AIBotTestQuestion_aiBotTestSectionId_fkey" FOREIGN KEY ("aiBotTestSectionId") REFERENCES "AIBotTestSection"("aiBotTestSectionId") ON DELETE SET NULL ON UPDATE CASCADE;
