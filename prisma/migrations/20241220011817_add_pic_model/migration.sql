-- CreateTable
CREATE TABLE "PicHistory" (
    "picHistoryId" SERIAL NOT NULL,
    "applicationId" INTEGER NOT NULL,
    "userId" INTEGER NOT NULL,
    "roleId" INTEGER NOT NULL,
    "description" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "PicHistory_pkey" PRIMARY KEY ("picHistoryId")
);

-- CreateIndex
CREATE INDEX "PicHistory_applicationId_userId_roleId_idx" ON "PicHistory"("applicationId", "userId", "roleId");

-- AddForeignKey
ALTER TABLE "PicHistory" ADD CONSTRAINT "PicHistory_applicationId_fkey" FOREIGN KEY ("applicationId") REFERENCES "Application"("applicationId") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "PicHistory" ADD CONSTRAINT "PicHistory_userId_roleId_fkey" FOREIGN KEY ("userId", "roleId") REFERENCES "UserRole"("userId", "roleId") ON DELETE RESTRICT ON UPDATE CASCADE;
