/*
  Warnings:

  - You are about to drop the column `roleId` on the `ClientPermission` table. All the data in the column will be lost.
  - A unique constraint covering the columns `[userId]` on the table `ClientPermission` will be added. If there are existing duplicate values, this will fail.
  - Added the required column `userId` to the `ClientPermission` table without a default value. This is not possible if the table is not empty.

*/
-- DropForeignKey
ALTER TABLE "ClientPermission" DROP CONSTRAINT "ClientPermission_roleId_fkey";

-- DropIndex
DROP INDEX "ClientPermission_roleId_idx";

-- DropIndex
DROP INDEX "ClientPermission_roleId_key";

-- AlterTable
ALTER TABLE "ClientPermission" DROP COLUMN "roleId",
ADD COLUMN     "userId" INTEGER NOT NULL;

-- CreateIndex
CREATE UNIQUE INDEX "ClientPermission_userId_key" ON "ClientPermission"("userId");

-- CreateIndex
CREATE INDEX "ClientPermission_userId_idx" ON "ClientPermission"("userId");

-- AddForeignKey
ALTER TABLE "ClientPermission" ADD CONSTRAINT "ClientPermission_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("userId") ON DELETE CASCADE ON UPDATE CASCADE;
