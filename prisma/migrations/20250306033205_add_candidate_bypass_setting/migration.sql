-- AlterTable
ALTER TABLE "Profile" ADD COLUMN     "bypassSettingId" INTEGER;

-- CreateTable
CREATE TABLE "CandidateBypassSettings" (
    "id" SERIAL NOT NULL,
    "isSkipConversation" BOOLEAN NOT NULL DEFAULT false,
    "isSkipLiveCoding" BOOLEAN NOT NULL DEFAULT false,
    "isSkipAiProctor" BOOLEAN NOT NULL DEFAULT false,
    "bypassReason" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "CandidateBypassSettings_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "Profile_userId_roleId_bypassSettingId_idx" ON "Profile"("userId", "roleId", "bypassSettingId");

-- AddForeignKey
ALTER TABLE "Profile" ADD CONSTRAINT "Profile_bypassSettingId_fkey" FOREIGN KEY ("bypassSettingId") REFERENCES "CandidateBypassSettings"("id") ON DELETE SET NULL ON UPDATE CASCADE;
