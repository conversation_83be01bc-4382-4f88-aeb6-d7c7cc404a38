/*
  Warnings:

  - Added the required column `categoryId` to the `WorkingPosition` table without a default value. This is not possible if the table is not empty.

*/
-- AlterTable
ALTER TABLE "WorkingPosition" ADD COLUMN     "categoryId" INTEGER NOT NULL;

-- CreateTable
CREATE TABLE "WorkingPositionCategory" (
    "categoryId" SERIAL NOT NULL,
    "name" TEXT NOT NULL,
    "description" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "WorkingPositionCategory_pkey" PRIMARY KEY ("categoryId")
);

-- CreateIndex
CREATE INDEX "WorkingPosition_categoryId_idx" ON "WorkingPosition"("categoryId");

-- AddForeignKey
ALTER TABLE "WorkingPosition" ADD CONSTRAINT "WorkingPosition_categoryId_fkey" FOREIGN KEY ("categoryId") REFERENCES "WorkingPositionCategory"("categoryId") ON DELETE RESTRICT ON UPDATE CASCADE;
