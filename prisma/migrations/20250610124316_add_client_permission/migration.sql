-- CreateTable
CREATE TABLE "ClientPermission" (
    "clientPermissionId" SERIAL NOT NULL,
    "roleId" INTEGER NOT NULL,
    "isCanCustomInterview" BOOLEAN NOT NULL DEFAULT false,
    "isCan<PERSON>hortlist" BOOLEAN NOT NULL DEFAULT false,
    "isCanJobOpenings" BOOLEAN NOT NULL DEFAULT false,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "ClientPermission_pkey" PRIMARY KEY ("clientPermissionId")
);

-- CreateIndex
CREATE UNIQUE INDEX "ClientPermission_roleId_key" ON "ClientPermission"("roleId");

-- CreateIndex
CREATE INDEX "ClientPermission_roleId_idx" ON "ClientPermission"("roleId");

-- AddF<PERSON>ignKey
ALTER TABLE "ClientPermission" ADD CONSTRAINT "ClientPermission_roleId_fkey" FOREIGN KEY ("roleId") REFERENCES "Role"("roleId") ON DELETE CASCADE ON UPDATE CASCADE;
