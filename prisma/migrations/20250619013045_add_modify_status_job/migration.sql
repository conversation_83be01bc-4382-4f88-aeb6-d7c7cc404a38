/*
  Warnings:

  - The values [URGENT,ON_HOLD,DRAFT,REQUESTING_TO_CLOSE,REQUESTING_TO_REOPEN,UNDER_REVIEW] on the enum `JobStatus` will be removed. If these variants are still used in the database, this will fail.
  - You are about to drop the column `reasonToUnsubmit` on the `JobActionReason` table. All the data in the column will be lost.

*/
-- AlterEnum
BEGIN;
CREATE TYPE "JobStatus_new" AS ENUM ('OPEN', 'CLOSED');
ALTER TABLE "Job" ALTER COLUMN "status" TYPE "JobStatus_new" USING ("status"::text::"JobStatus_new");
ALTER TYPE "JobStatus" RENAME TO "JobStatus_old";
ALTER TYPE "JobStatus_new" RENAME TO "JobStatus";
DROP TYPE "JobStatus_old";
COMMIT;

-- AlterTable
ALTER TABLE "JobActionReason" DROP COLUMN "reasonToUnsubmit";
