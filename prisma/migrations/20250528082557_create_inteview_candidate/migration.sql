-- CreateEnum
CREATE TYPE "InterviewCandidateStatus" AS ENUM ('PROCESSING', 'HIRED', 'REJECTED');

-- CreateTable
CREATE TABLE "InterviewCandidate" (
    "interviewCandidateId" TEXT NOT NULL,
    "interviewGroupId" TEXT NOT NULL,
    "profileId" INTEGER,
    "name" TEXT NOT NULL,
    "yearOfExperience" INTEGER,
    "status" "InterviewCandidateStatus" NOT NULL DEFAULT 'PROCESSING',
    "summary" TEXT,
    "favorite" BOOLEAN DEFAULT false,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "InterviewCandidate_pkey" PRIMARY KEY ("interviewCandidateId")
);

-- CreateTable
CREATE TABLE "_InterviewCandidateToTechnicalSkill" (
    "A" TEXT NOT NULL,
    "B" INTEGER NOT NULL
);

-- CreateIndex
CREATE INDEX "InterviewCandidate_interviewGroupId_idx" ON "InterviewCandidate"("interviewGroupId");

-- CreateIndex
CREATE UNIQUE INDEX "_InterviewCandidateToTechnicalSkill_AB_unique" ON "_InterviewCandidateToTechnicalSkill"("A", "B");

-- CreateIndex
CREATE INDEX "_InterviewCandidateToTechnicalSkill_B_index" ON "_InterviewCandidateToTechnicalSkill"("B");

-- AddForeignKey
ALTER TABLE "InterviewCandidate" ADD CONSTRAINT "InterviewCandidate_interviewGroupId_fkey" FOREIGN KEY ("interviewGroupId") REFERENCES "InterviewGroup"("interviewGroupId") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "_InterviewCandidateToTechnicalSkill" ADD CONSTRAINT "_InterviewCandidateToTechnicalSkill_A_fkey" FOREIGN KEY ("A") REFERENCES "InterviewCandidate"("interviewCandidateId") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "_InterviewCandidateToTechnicalSkill" ADD CONSTRAINT "_InterviewCandidateToTechnicalSkill_B_fkey" FOREIGN KEY ("B") REFERENCES "TechnicalSkill"("technicalSkillId") ON DELETE CASCADE ON UPDATE CASCADE;
