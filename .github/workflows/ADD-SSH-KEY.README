# Adding SSH Key to Server

This guide will walk you through the process of creating an SSH key and adding it to a server.

## Step 1: Generate SSH Key Pair

1. Open a terminal or command prompt.
2. Run the following command to generate a new SSH key pair:
    ```
    ssh-keygen -t rsa -b 4096 -C "<EMAIL>"
    ```
    Replace `"<EMAIL>"` with your own email address.
3. You will be prompted to enter a file path to save the key pair. Press Enter to accept the default location (`~/.ssh/id_rsa`).
4. You will also be prompted to enter a passphrase. It is recommended to set a passphrase for added security, but you can leave it blank if you prefer.

## Step 2: Cat Public Key

1. Run the following command to display the public key:
    ```
    cat ~/.ssh/id_rsa.pub
    ```

## Step 3: Add Public Key to SSH Authorization on Server

1. Copy the public key from the previous step.
2. Connect to the server using SSH.
3. Open the `~/.ssh/authorized_keys` file on the server using a text editor.
4. Paste the public key at the end of the file.
5. Save and close the file.
6. You should now be able to connect to the server without entering a password.

