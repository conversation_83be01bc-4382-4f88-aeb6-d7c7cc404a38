name: CI/CD Pipeline

on:
  pull_request:
    types: [closed]
    branches: [master, staging]

jobs:
  deploy:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Deploy to staging server
        if: github.event.pull_request.base.ref == 'staging' && github.event.pull_request.merged == true
        uses: ./.github/actions/deploy-with-ssh
        with:
          user: ${{ secrets.USER }}
          host_name: ${{ secrets.STAGING_HOST_NAME }}
          branch: ${{ github.event.pull_request.base.ref }}
          ssh_private_key: ${{ secrets.SSH_PRIVATE_KEY }}

      - name: Deploy to production server
        if: github.event.pull_request.base.ref == 'master' && github.event.pull_request.merged == true
        uses: ./.github/actions/deploy-with-ssh
        with:
          user: ${{ secrets.USER }}
          host_name: ${{ secrets.PROD_HOST_NAME }}
          branch: ${{ github.event.pull_request.base.ref }}
          ssh_private_key: ${{ secrets.SSH_PRIVATE_KEY }}
          