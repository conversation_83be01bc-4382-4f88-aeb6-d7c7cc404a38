name: 'Deploy with SSH'

description: 'SSH to server, navigate to folder, pull latest code, and run script'

inputs:
  host_name:
    description: 'The address of the server'
    required: true
  user:
    description: 'The name of user'
    required: true 
  branch:
    description: 'The branch to pull'
    required: true
  ssh_private_key:
    description: 'private key for ssh' 
    required: true

runs:
  using: 'composite'
  steps:
    - name: SSH and Deploy
      shell: bash
      run: |
        mkdir -p ~/.ssh
        echo "${{ inputs.ssh_private_key }}" > ~/.ssh/id_rsa
        chmod 600 ~/.ssh/id_rsa
        ssh-keyscan -H ${{ inputs.host_name }} >> ~/.ssh/known_hosts
        ssh -o StrictHostKeyChecking=no ${{ inputs.user }}@${{ inputs.host_name }} << EOF
          cd ~/job-apps/jobs-worker                 
          ./run-${{ inputs.branch }}.sh
        EOF
