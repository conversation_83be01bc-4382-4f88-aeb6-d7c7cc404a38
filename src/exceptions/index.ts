import { HttpException, HttpStatus } from '@nestjs/common';

export class UserNotFoundException extends HttpException {
  constructor(message?: string) {
    super(message || 'User Not Found', HttpStatus.NOT_FOUND);
  }
}

export class JobSkillNotFoundException extends HttpException {
  constructor(message?: string) {
    super(message || 'Job skill not found!!!', HttpStatus.NOT_FOUND);
  }
}

export class UnauthorizedException extends HttpException {
  constructor(message?: string) {
    super(message || 'Unauthorized', HttpStatus.UNAUTHORIZED);
  }
}

export class BadRequestException extends HttpException {
  constructor(message?: string) {
    super(message || 'Bad Request', HttpStatus.BAD_REQUEST);
  }
}
