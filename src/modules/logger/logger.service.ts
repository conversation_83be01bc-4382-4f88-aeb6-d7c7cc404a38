import * as winston from 'winston';
import * as chalk from 'chalk';
import { createLogger, LoggerOptions } from 'winston';
import { Injectable, LoggerService } from '@nestjs/common';
import * as Sentry from '@sentry/node';

@Injectable()
export class MyLogger implements LoggerService {
  private readonly logger;
  private level = 'info';
  private context: string;
  private static LOGS_PATH = 'storage/logs';

  constructor() {
    this.logger = createLogger(this.getLoggerOptions(this.level));
    this.setContext('Main');

    Sentry.init({
      dsn: process.env.SENTRY_DSN,
      environment: process.env.NODE_ENV || 'development',
      tracesSampleRate: 1.0,
    });

    console.log('✅ Sentry Initialized in Logger');
  }

  public getLoggerOptions(level: string): LoggerOptions {
    return {
      level: level,
      transports: [
        new winston.transports.File({
          filename: `${MyLogger.LOGS_PATH}/${level}.log`,
        }),
      ],
    };
  }

  public setContext(context: string): this {
    this.context = context;
    return this;
  }

  public setLevel(level: string): this {
    this.level = level;
    const loggerOptions = this.getLoggerOptions(level);
    this.overrideOptions(loggerOptions);
    return this;
  }

  log(message: string): void {
    this.setLevel('info');
    const currentDate = new Date();
    this.logger.info(message, {
      timestamp: currentDate.toISOString(),
      context: this.context,
    });
    this.logToConsole('info', message);
  }

  error(message: string, trace?: any): void {
    this.setLevel('error');
    const currentDate = new Date();
    Sentry.captureException(message);

    this.logger.error(`${message} -> (${trace || 'trace not provided !'})`, {
      timestamp: currentDate.toISOString(),
      context: this.context,
    });
    this.logToConsole('error', message);
  }

  warn(message: string): void {
    this.setLevel('warn');
    const currentDate = new Date();
    this.logger.warn(message, {
      timestamp: currentDate.toISOString(),
      context: this.context,
    });
    this.logToConsole('warn', message);
  }

  info(message: string): void {
    this.setLevel('info');
    const currentDate = new Date();
    this.logger.info(message, {
      timestamp: currentDate.toISOString(),
      context: this.context,
    });
    this.logToConsole('info', message);
  }

  debug(message: string): void {
    this.setLevel('debug');
    const currentDate = new Date();
    this.logger.info(message, {
      timestamp: currentDate.toISOString(),
      context: this.context,
    });
    this.logToConsole('debug', message);
  }

  overrideOptions(options: LoggerOptions): void {
    this.logger.configure(options);
  }

  private logToConsole(level: string, message: string): void {
    let result;
    const color = chalk;
    const currentDate = new Date();
    const time = `${currentDate.getHours()}:${currentDate.getMinutes()}:${currentDate.getSeconds()}`;

    switch (level) {
      default:
      case 'info':
        result = `[${color.blue('INFO')}] ${color.dim.yellow.bold.underline(time)} [${color.green(this.context)}] ${message}`;
        break;
      case 'error':
        result = `[${color.red('ERR')}] ${color.dim.yellow.bold.underline(time)} [${color.green(this.context)}] ${message}`;
        break;
      case 'warn':
        result = `[${color.yellow('WARN')}] ${color.dim.yellow.bold.underline(time)} [${color.green(this.context)}] ${message}`;
        break;
    }

    console.log(result); // TODO: DON'T remove this console.log
    this.logger.close();
  }
}
