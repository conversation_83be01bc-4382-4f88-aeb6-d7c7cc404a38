import {
  Injectable,
  InternalServerErrorException,
  Logger,
} from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { WebClient } from '@slack/web-api';
import { TestResultDto } from './dto/testResult.dto';
import { INotification } from 'src/utils/interfaces/notification.interface';
import { getUserName } from 'src/shared/helpers/mail.helpers';

@Injectable()
export class SlackService {
  private readonly client: WebClient;
  private readonly channel: string;
  private readonly logger = new Logger(SlackService.name);  

  constructor(private readonly configService: ConfigService) {
    const slackToken = this.configService.get<string>('SLACK_BOT_TOKEN');
    this.client = new WebClient(slackToken);
    this.channel =
      this.configService.get<string>('SLACK_CHANNEL') || '#server-health';
  }

  async sendTestResults(notificationParams: INotification): Promise<void> {
    try {
      const foundationTest = notificationParams.foundationTest;
      const { application, technicalTest, englishTest, aiBotTest, codingTest } =
        foundationTest;
      const { job, userRole } = application;
      const { profile, user } = userRole;
      const blocks = this.createTestResultMessageBlocks({
        date: new Date().toLocaleString(),
        name: getUserName(profile),
        email: user.email,
        level: job.title,
        appliedOn: application.appliedDate.toLocaleString(),
        technicalScore: technicalTest.score,
        technicalStatus: technicalTest.status,
        englishScore: englishTest.score,
        englishStatus: englishTest.status,
        aiBotInterviewScore: aiBotTest.score,
        aiBotStatus: aiBotTest.status,
        codingScore: codingTest.score,
        codingStatus: codingTest.status,
      });

      await this.client.chat.postMessage({
        channel: this.channel,
        blocks,
        text: 'Candidate Performance Evaluation'  
      });

      this.logger.log('Message sent to Slack successfully!');
    } catch (error) {
      throw new InternalServerErrorException('Failed to post message to Slack');
    }
  }

  private createTestResultMessageBlocks(testResult: TestResultDto) {
    const {
      date, name, email, level, appliedOn,
      technicalScore, technicalStatus,
      englishScore, englishStatus,
      aiBotInterviewScore, aiBotStatus,
      codingScore, codingStatus
    } = testResult;

    return [
      {
        type: 'header',
        text: {
          type: 'plain_text',
          text: '📊 Candidate Performance Evaluation',
          emoji: true
        }
      },
      {
        type: 'section',
        text: {
          type: 'mrkdwn',
          text: `*Date:* ${date}\n\n*Here's a breakdown of the information provided:*`
        }
      },
      {
        type: 'section',
        fields: [
          { type: 'mrkdwn', text: `*Name:* ${name}` },
          { type: 'mrkdwn', text: `*Email:* ${email}` },
          { type: 'mrkdwn', text: `*Level:* ${level}` },
          { type: 'mrkdwn', text: `*Applied on:* ${appliedOn}` }
        ]
      },
      {
        type: 'section',
        fields: [
          {
            type: 'mrkdwn',
            text: `*Technical score:* ${technicalScore}/100 - *Status:* ${technicalStatus}`,
          },
          {
            type: 'mrkdwn',
            text: `*English score:* ${englishScore}/100 - *Status:* ${englishStatus}`,
          },
          {
            type: 'mrkdwn',
            text: `*AI Bot Interview:* ${aiBotInterviewScore}/100 - *Status:* ${aiBotStatus}`,
          },
          {
            type: 'mrkdwn',
            text: `*Coding score:* ${codingScore}/10 - *Status:* ${codingStatus}`,
          },
        ]
      },
      { type: 'divider' }
    ];
  }
}
