import { <PERSON>, Post, Body } from '@nestjs/common';
import { SlackService } from './slack.service';
import { INotification } from 'src/utils/interfaces/notification.interface';

@Controller('slack')
export class SlackController {
  constructor(private readonly slackService: SlackService) {}

  @Post('send')
  async sendSlackMessage(
    @Body() body: INotification,
  ): Promise<{ message: string }> {
    await this.slackService.sendTestResults(body);
    return { message: 'Slack message sent successfully!' };
  }
}
