import { TestStatus } from '@prisma/client';
import {
  IsString,
  IsDateString,
  IsEmail,
  IsInt,
  Min,
  Max,
  IsEnum,
} from 'class-validator';

export class TestResultDto {
  @IsDateString()
  date: string;

  @IsString()
  name: string;

  @IsEmail()
  email: string;

  @IsString()
  level: string;

  @IsDateString()
  appliedOn: string;

  @IsInt()
  @Min(0)
  @Max(100)
  technicalScore: number;

  @IsInt()
  @Min(0)
  @Max(100)  
  englishScore: number;

  @IsInt()
  @Min(0)
  @Max(100)
  aiBotInterviewScore: number;

  @IsInt()
  @Min(0)
  @Max(100)
  codingScore: number;

  @IsEnum(TestStatus)
  technicalStatus: TestStatus;
  
  @IsEnum(TestStatus)
  englishStatus: TestStatus;

  @IsEnum(TestStatus)
  aiBotStatus: TestStatus;

  @IsEnum(TestStatus)
  codingStatus: TestStatus;
}
