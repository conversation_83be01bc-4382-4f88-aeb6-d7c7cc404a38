import { Injectable } from '@nestjs/common';
import { InjectQueue } from '@nestjs/bull';
import { Queue } from 'bull';
import { Interval } from '@nestjs/schedule';
import { MasterFormStatus, TestStatus } from '@prisma/client';

import {
  IMasterFormService,
  IMasterForm,
} from 'src/utils/interfaces/master-form.interface';
import { QueueName, MasterFormTask } from 'src/utils/enums';
import { PrismaService } from '../prisma/prisma.service';

@Injectable()
export class MasterFormService implements IMasterFormService {
  constructor(
    private prismaService: PrismaService,
    @InjectQueue(QueueName.MASTER_FORM_QUEUE)
    private masterFormQueue: Queue<IMasterForm>,
  ) {}

  @Interval(1 * 60 * 1000)
  async masterFormScanCV(): Promise<void> {
    const resumes = await this.prismaService.resumeUploadSection.findMany({
      where: {
        status: MasterFormStatus.PENDING,
      },
      include: {
        masterForm: true,
      },
    });

    for (const resume of resumes) {
      this.masterFormQueue.add(
        MasterFormTask.MASTER_FORM_SCAN_TASK,
        { resume },
        {
          jobId: `masterFormUploadResume-${resume.resumeUploadSectionId}`,
        },
      );
    }
  }

  @Interval(1 * 60 * 1000)
  async masterFormAiBotTestRecording(): Promise<void> {
    const aiBotTestRooms = await this.prismaService.aIBotTestRoom.findMany({
      where: {
        recordingStatus: {
          in: ['in-progress', 'sent'],
        },
      },
    });

    this.masterFormQueue.add(
      MasterFormTask.MASTER_FORM_AI_BOT_TEST_RECORDING_TASK,
      { aiBotTestRooms },
      { removeOnComplete: true },
    );
  }
}
