import { Modu<PERSON> } from '@nestjs/common';
import { BullModule } from '@nestjs/bull';
import { PrismaModule } from '../prisma/prisma.module';

import { QueueName } from 'src/utils/enums';
import { MasterFormService } from './master-form.service';
import { MasterFormController } from './master-form.controller';
import { TaskMaterFormConsumer } from './master-form.consumer';

@Module({
  imports: [
    BullModule.registerQueue({
      name: QueueName.MASTER_FORM_QUEUE,
    }),
    PrismaModule,
  ],
  controllers: [MasterFormController],
  providers: [MasterFormService, TaskMaterFormConsumer],
  exports: [MasterFormService],
})
export class MasterFormModule {}
