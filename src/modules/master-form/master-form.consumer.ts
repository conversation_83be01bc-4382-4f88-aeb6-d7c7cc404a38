import { Processor, Process, OnQueueCompleted } from '@nestjs/bull';
import { Job } from 'bull';
import * as fs from 'fs';
import axios from 'axios';
import * as path from 'path';
import * as FormData from 'form-data';
import {
  MasterFormStatus,
  TestStatus,
  AIBotTestRoomStatus,
  ChannelType,
} from '@prisma/client';

import { PrismaService } from '../prisma/prisma.service';
import { QueueName, MasterFormTask } from './../../utils/enums';
import {
  ITaskMaterFormConsumer,
  IMasterForm,
} from './../../utils/interfaces/master-form.interface';
import { BadRequestException } from 'src/exceptions';
import {
  ScanningCvResponse,
  ScanningSummaryResponse,
  CandidateDetailV3Response,
} from 'src/utils/types/cv-scanning.type';
import {
  MasterFormCvScanning,
  UserConversationResponse,
  MasterFormEnglishScoringResponse,
  MasterFormResumeContent,
  MasterFormWorkPreferenceContent,
  CreateProfile,
  MasterFormUserResponse,
  DailyRecordingResponse,
} from 'src/utils/types/master-form.type';
import { normalizeDate } from 'src/shared/helpers/master-form.helpers';

@Processor(QueueName.MASTER_FORM_QUEUE)
export class TaskMaterFormConsumer implements ITaskMaterFormConsumer {
  private readonly FILE_DIRECTORY = 'storage/master-form';
  private readonly FILE_PATH = path.join(this.FILE_DIRECTORY, 'cv.pdf');
  private readonly CV_SCAN_URL = process.env.CV_SCAN_URL;
  private readonly ML_TOKEN = process.env.ML_TOKEN;
  private readonly DAILY_API_KEY = process.env.DAILY_API_KEY;
  private readonly ML_URL = process.env.ML_URL;
  private readonly DAILY_URL = process.env.DAILY_API_URL;
  private readonly DAILY_S3_URL = process.env.DAILY_S3_URL;

  private readonly CV_SCANNING_URL = `${this.CV_SCAN_URL}/api/hr-internal/upload-cv`;
  private readonly CV_CANDIDATE_SCANNING_V3_URL = `${this.CV_SCAN_URL}/llm/extract-cv-info`;
  private readonly AI_ENGLISH_SCORING_URL = `${this.ML_URL}/api/rag/conversation-scoring/english-scoring`;
  private readonly DAILY_RECORDING_URL = `${this.DAILY_URL}/recordings`;
  constructor(private readonly prismaService: PrismaService) {}

  async downloadFileFromS3(s3Url: string): Promise<boolean> {
    try {
      if (!fs.existsSync(this.FILE_DIRECTORY)) {
        fs.mkdirSync(this.FILE_DIRECTORY, { recursive: true });
      }

      const writer = fs.createWriteStream(this.FILE_PATH);
      const response = await axios({
        url: s3Url,
        method: 'GET',
        responseType: 'stream',
      });

      return new Promise((resolve, reject) => {
        response.data.pipe(writer);
        writer.on('finish', () => {
          console.log('File downloaded successfully');
          resolve(true);
        });
        writer.on('error', (error) => {
          reject(error);
          console.log('Error downloading file:', error);
          throw new BadRequestException(`Error downloading file`);
        });
      });
    } catch (error) {
      console.log('Error downloading file:', error);
      throw new BadRequestException(`Error downloading file`);
    }
  }

  async uploadFileToML(): Promise<ScanningCvResponse> {
    try {
      const formData = new FormData();
      formData.append('file', fs.createReadStream(this.FILE_PATH));

      const response = await axios.post<ScanningCvResponse>(
        this.CV_SCANNING_URL,
        formData,
        {
          headers: {
            ...formData.getHeaders(),
            Authorization: `Bearer ${this.ML_TOKEN}`,
          },
        },
      );
      console.log('Scanning CV successfully');
      return response.data;
    } catch (error) {
      console.log('Error scanning CV:', error);
      throw new BadRequestException(`Error scanning CV`);
    }
  }

  async postToApi<T>(url: string, data: any): Promise<T> {
    try {
      const response = await axios.post<T>(url, data, {
        headers: { Authorization: `Bearer ${this.ML_TOKEN}` },
      });

      console.log('Processing candidate detail successfully');
      return response.data;
    } catch (error) {
      console.log('Error', error);
      throw new BadRequestException();
    }
  }

  async processCandidateDetail(s3_url: string, email: string) {
    try {
      const candidateDetail = await this.postToApi<CandidateDetailV3Response>(
        this.CV_CANDIDATE_SCANNING_V3_URL,
        {
          email,
          cv_url: s3_url,
        },
      );
      return {
        candidateDetail,
      };
    } catch (error) {
      console.log(error.message);
      throw new Error(`Error parse candidate detail with cv ${s3_url}!!!`);
    }
  }

  private isPresent(dateStr: string): boolean | null {
    if (!dateStr) return null;
    return dateStr.toLowerCase() === 'present' ? true : null;
  }

  private async createAccademyHistory(
    profileId: number,
    masterFormResume: MasterFormCvScanning,
  ) {
    try {
      const academyData = masterFormResume?.academy ?? [];

      for (const academyHistory of academyData) {
        await this.prismaService.academyHistory.create({
          data: {
            profileId,
            universityName: academyHistory.universityName ?? null,
            major: academyHistory.major ?? null,
            startDate: normalizeDate(academyHistory.startDate),
            endDate: normalizeDate(academyHistory.endDate),
            currentlyPursuing: this.isPresent(academyHistory.endDate),
          },
        });
      }
    } catch (error) {
      console.log('Fail to sync academy to profile: ', error.message);
    }
  }

  private async createEmploymentHistory(
    profileId: number,
    masterFormResume: MasterFormCvScanning,
  ) {
    try {
      const employmentData = masterFormResume?.employment ?? [];
      for (const employment of employmentData) {
        const createdEmployment =
          await this.prismaService.employmentHistory.create({
            data: {
              profileId,
              companyName: employment.companyName ?? null,
              startDate: normalizeDate(employment.startDate),
              endDate: normalizeDate(employment.endDate),
              description: employment.description ? employment.description : [],
              currentlyWorking: this.isPresent(employment.endDate),
            },
          });

        const positions = employment.workingPositionHistory ?? [];
        for (const position of positions) {
          if (position.position)
            await this.prismaService.workingPositionHistory.create({
              data: {
                employmentHistoryId: createdEmployment.employmentHistoryId,
                position: position.position,
              },
            });
        }
      }
    } catch (error) {
      console.log('Fail to sync academy to profile: ', error.message);
    }
  }

  private async createPersonalProject(
    profileId: number,
    masterFormResume: MasterFormCvScanning,
  ) {
    try {
      if (masterFormResume.personalProject.length > 0) {
        await this.prismaService.personalProject.createMany({
          data: masterFormResume.personalProject.map((project) => ({
            profileId,
            projectName: project.projectName,
            startDate: normalizeDate(project.startDate),
            endDate: normalizeDate(project.endDate),
            workingPosition: project.workingPosition,
            description: project.description,
          })),
        });
      }
    } catch (error) {
      console.log('Fail to sync academy to profile: ', error.message);
    }
  }

  private async createAward(
    profileId: number,
    masterFormResume: MasterFormCvScanning,
  ) {
    try {
      if (masterFormResume.award.length > 0) {
        await this.prismaService.award.createMany({
          data: masterFormResume.award.map((award) => ({
            profileId,
            title: award.title,
            date: normalizeDate(award.date),
          })),
        });
      }
    } catch (error) {
      console.log('Fail to sync award to profile: ', error.message);
    }
  }

  private async createPublication(
    profileId: number,
    masterFormResume: MasterFormCvScanning,
  ) {
    try {
      const publicationData = masterFormResume.publication;

      for (const publication of publicationData) {
        await this.prismaService.publication.create({
          data: {
            profileId,
            title: publication.title ?? null,
            publicationName: publication.publicationName ?? null,
            dateOfPublication: publication.dateOfPublication ?? null,
            link: publication.link ?? null,
            authors: publication.authors ?? [],
            description: publication.description ?? null,
          },
        });
      }
    } catch (error) {
      console.log('Fail to sync publication to profile: ', error.message);
    }
  }

  private async createChannel(
    profileId: number,
    masterFormResume: MasterFormCvScanning,
  ) {
    try {
      const channelData = masterFormResume.channel;

      const channels = await Promise.all(
        channelData.map(async (channel) => {
          const platformChannel =
            await this.prismaService.platformChannel.findFirst({
              where: {
                platFormName: channel.platform[0].platformName,
              },
            });

          return {
            profileId: profileId ?? null,
            type: (channel.type as ChannelType) ?? null,
            platformUrl: channel.platformUrl ?? null,
            platformId: platformChannel?.platformId ?? null,
          };
        }),
      );

      if (channels.length > 0) {
        await this.prismaService.channel.createMany({ data: channels });
      }
    } catch (error) {
      console.log('Fail to sync channel to profile: ', error.message);
    }
  }

  private async createTechnicalSkill(
    profileId: number,
    masterFormResume: MasterFormCvScanning,
  ) {
    try {
      const skills = await this.prismaService.technicalSkill.findMany({
        where: {
          skillName: {
            in: masterFormResume.technicalSkill.map((s) => s.skillName),
          },
        },
      });

      const userSkills = skills.map((skill) => ({
        profileId,
        technicalSkillId: skill.technicalSkillId,
      }));

      if (userSkills.length > 0) {
        await this.prismaService.userTechnicalSkill.createMany({
          data: userSkills,
        });
      }
    } catch (error) {
      console.log('Fail to sync technical skill to profile: ', error.message);
    }
  }

  private async createCertification(
    userId: number,
    roleId: number,
    masterFormResume: MasterFormCvScanning,
  ) {
    try {
      const certificationData = masterFormResume.certification ?? [];

      for (const cert of certificationData) {
        const certification = await this.prismaService.certification.findFirst({
          where: { name: cert.name },
        });

        if (!certification) continue;

        const existingUserCert =
          await this.prismaService.userCertification.findFirst({
            where: {
              userId,
              roleId,
              certificationId: certification.certificationId,
            },
          });

        if (existingUserCert) continue;

        await this.prismaService.userCertification.create({
          data: {
            userId,
            roleId,
            certificationId: certification.certificationId,
            score: null,
            issuer: null,
            attachmentUrl: null,
            issueDate: cert.issueDate ? normalizeDate(cert.issueDate) : null,
          },
        });
      }
    } catch (error) {
      console.log('Fail to sync certification to profile: ', error.message);
    }
  }

  @Process(MasterFormTask.MASTER_FORM_SCAN_TASK)
  async processMasterFormScanCV(job: Job<IMasterForm>): Promise<void> {
    console.log('Start processing job...', job.data);

    const { resumeUrl, masterFormEmail } = job.data.resume;
    const { candidateDetail } = await this.processCandidateDetail(
      resumeUrl,
      masterFormEmail,
    );
    const {
      summary,
      basic_information,
      education,
      experience_work,
      experience_projects,
      recognition_publications,
      recognition_awards,
      skills_technical,
      skills_languages,
      skills_certifications,
    } = candidateDetail.data;

    const masterFormCvScanning: MasterFormCvScanning = {
      cv_scanned_url: resumeUrl,
      summary,
      birthDate: basic_information.date_of_birth,
      emailAddress: basic_information.email_address,
      firstName: basic_information.first_name,
      lastName: basic_information.last_name,
      phoneNumber: basic_information.phone_number?.number ?? null,
      academy: education.map((academicHistory) => {
        return {
          universityName: academicHistory.university_name,
          major: academicHistory.major,
          degree: academicHistory.degree,
          gpa: academicHistory.gpa,
          startDate: academicHistory.start_date,
          endDate: academicHistory.end_date,
          proof: [],
        };
      }),
      technicalSkill: skills_technical.map((skill) => {
        return { skillName: skill };
      }),
      employment: experience_work.map((workingPositionHistory) => {
        return {
          companyName: workingPositionHistory.company_name,
          startDate: workingPositionHistory.start_date,
          endDate: workingPositionHistory.end_date,
          description: [workingPositionHistory.description],
          currentlyWorking: false,
          workingPositionHistory: [
            {
              position: workingPositionHistory.position,
              positionStartDate: workingPositionHistory.start_date,
              positionEndDate: workingPositionHistory.end_dPate,
              skills: workingPositionHistory.skills,
            },
          ],
        };
      }),
      channel: [
        ...basic_information.coding_channels.map((c) => {
          return {
            type: ChannelType.CODING,
            platformUrl: c.url,
            platform: [
              {
                platformName: c.channel,
              },
            ],
          };
        }),
        ...basic_information.portfolio_channels.map((c) => {
          return {
            type: ChannelType.PORTFOLIO,
            platformUrl: c.url,
            platform: [
              {
                platformName: c.channel,
              },
            ],
          };
        }),
        ...basic_information.social_media_channels.map((c) => {
          return {
            type: ChannelType.SOCIAL_MEDIA,
            platformUrl: c.url,
            platform: [
              {
                platformName: c.channel,
              },
            ],
          };
        }),
      ],
      personalProject: experience_projects.map((personalProject) => {
        return {
          projectName: personalProject.project_name,
          startDate: personalProject.start_date,
          endDate: personalProject.end_date,
          workingPosition: personalProject.working_position,
          description: [personalProject.description],
        };
      }),
      award: recognition_awards.map((a) => {
        return {
          title: a.title,
          date: a.date,
        };
      }),
      publication: recognition_publications.map((p) => {
        return {
          title: p.title,
          dateOfPublication: p.publication_date,
          link: p.link,
          authors: [],
        };
      }),
      certification: skills_certifications.map((c) => {
        return {
          name: c.name,
          issueDate: c.issue_date,
        };
      }),
      language: skills_languages.map((l) => {
        return {
          languageName: l.language,
          level: l.proficiency,
        };
      }),
    };

    console.log('masterFormCvScanning', JSON.stringify(masterFormCvScanning));
    await this.prismaService.resumeUploadSection.update({
      where: { resumeUploadSectionId: job.data.resume.resumeUploadSectionId },
      data: {
        resumeContent: masterFormCvScanning,
        status: MasterFormStatus.COMPLETED,
      },
    });

    // sync data to profile
    const user = await this.prismaService.user.findFirst({
      where: {
        email: job.data.resume.masterFormEmail,
      },
      include: {
        userRoles: true,
      },
    });

    const profile = await this.prismaService.profile.findFirst({
      where: { userId: user.userId },
    });
    await this.createAccademyHistory(profile.profileId, masterFormCvScanning);
    await this.createEmploymentHistory(profile.profileId, masterFormCvScanning);
    await this.createPersonalProject(profile.profileId, masterFormCvScanning);
    await this.createAward(profile.profileId, masterFormCvScanning);
    await this.createCertification(
      user.userId,
      user.userRoles[0].roleId,
      masterFormCvScanning,
    );
    await this.createPublication(profile.profileId, masterFormCvScanning);
    await this.createTechnicalSkill(profile.profileId, masterFormCvScanning);
    await this.createChannel(profile.profileId, masterFormCvScanning);

    await this.prismaService.profile.update({
      where: { profileId: profile.profileId },
      data: {
        resumeSummary: summary,
        cv: [resumeUrl],
        firstName: masterFormCvScanning.firstName ?? '',
        lastName: masterFormCvScanning.lastName ?? '',
        birthDate: normalizeDate(masterFormCvScanning.birthDate),
      },
    });
  }

  @Process(MasterFormTask.MASTER_FORM_CODING_TEST_TASK)
  async processMasterFormCodingTest(job: Job<IMasterForm>): Promise<void> {
    console.log('Start processing job...', job.data);
  }

  @Process(MasterFormTask.MASTER_FORM_AI_BOT_TEST_TASK)
  async processMasterFormAiBotTest(job: Job<IMasterForm>): Promise<void> {
    console.log('Start processing job...', job.data);
    const {
      masterFormEmail,
      conversationId,
      aiBotTestSectionId,
      audioRecordUrl,
    } = job.data.aiBotTest;
    const mlResponse = await axios.get<UserConversationResponse>(
      `${this.ML_URL}/api/rag/conversation-message/list?user-id=${masterFormEmail}&conversation-id=${conversationId}`,
    );
    const conversations = mlResponse.data.data.reverse();
    for (const conversation of conversations) {
      await this.prismaService.aIBotTestQuestion.create({
        data: {
          aiBotTestSectionId,
          messageId: conversation.message_id,
          messageSender: conversation.sender_id,
          messageSenderContent: conversation.content,
          score: 0,
        },
      });

      await this.prismaService.aIBotTestSection.update({
        where: { aiBotTestSectionId },
        data: {
          status: TestStatus.TEST_COMPLETED,
          mlConversationResult: JSON.parse(JSON.stringify(conversations)),
        },
      });
    }
    if (conversationId)
      await this.prismaService.englishTestSection.create({
        data: {
          status: TestStatus.TEST_PENDING,
          userAnswer: audioRecordUrl,
          // webcamRecordUrl: audioRecordUrl,
          conversationId,
          score: 0,
          question: '',
          masterForm: {
            connect: {
              masterFormEmail,
            },
          },
        },
      });
  }

  @Process(MasterFormTask.MASTER_FORM_AI_BOT_TEST_RECORDING_TASK)
  async processAIBotTestRecordingTest(job: Job<IMasterForm>): Promise<void> {
    // console.log('Start processing job...', job.data);
    try {
      for (const room of job.data.aiBotTestRooms) {
        const recordingRes = await axios.get<DailyRecordingResponse>(
          `${this.DAILY_RECORDING_URL}/${room.recordingId}`,
          {
            headers: { Authorization: `Bearer ${this.DAILY_API_KEY}` },
          },
        );
        if (recordingRes.data.status === 'finished') {
          const aiBotTestRoom = await this.prismaService.aIBotTestRoom.update({
            where: {
              roomName: room.roomName,
            },
            data: {
              recordingStatus: 'finished',
              recordingUrl: `${this.DAILY_S3_URL}/${recordingRes.data.s3key}`,
              roomStatus: AIBotTestRoomStatus.INTERVIEW_COMPLETED,
            },
            include: {
              interviewCandidate: true,
            },
          });
          console.log(
            `Room ${room.roomName} already have recording url: ${this.DAILY_S3_URL}/${recordingRes.data.s3key}`,
          );
          // update interview completed time
          await this.prismaService.interviewCandidate.update({
            where: {
              interviewCandidateId: aiBotTestRoom.interviewCandidateId,
            },
            data: {
              interviewCompletedAt: new Date(),
            },
          });
        }
      }
    } catch (e) {
      console.log(e);
    }
  }

  @Process(MasterFormTask.MASTER_FORM_ENGLISH_TEST_TASK)
  async processMasterFormEnglishTest(job: Job<IMasterForm>): Promise<void> {
    console.log('Start processing job...', job.data);
    const { englishTestSectionId, conversationId } = job.data.englishTest;

    const response = await axios.post<MasterFormEnglishScoringResponse>(
      this.AI_ENGLISH_SCORING_URL,
      {
        conversation_id: conversationId,
      },
      {
        headers: { Authorization: `Bearer ${this.ML_TOKEN}` },
      },
    );

    await this.prismaService.englishTestSection.update({
      where: { englishTestSectionId },
      data: {
        status: TestStatus.TEST_COMPLETED,
        score:
          (response.data.data.conversation_score +
            response.data.data.speaking_score) /
          2,
        mlScoringResult: response.data.data,
      },
    });
  }

  @Process(MasterFormTask.MASTER_FORM_COMPLETION_TASK)
  async processMasterFormCompletion(job: Job<IMasterForm>): Promise<void> {
    const { masterFormCompleted } = job.data;
    const { resume, workPreference, masterFormEmail } = masterFormCompleted;
    const resumeContent: MasterFormResumeContent = JSON.parse(
      JSON.stringify(resume.resumeContent),
    );
    const workPreferenceContent: MasterFormWorkPreferenceContent = JSON.parse(
      JSON.stringify(workPreference),
    );

    const user = await this.prismaService.user.findFirst({
      where: {
        email: masterFormEmail,
      },
    });

    if (!user) throw new BadRequestException('User not found!!');

    const createProfile: CreateProfile = {
      userId: user.userId,
      summary: resumeContent.summary,
      firstName: resumeContent.firstName,
      lastName: resumeContent.lastName,
      birthDate: resumeContent.birthDate,
      middleName: resumeContent.middleName,
      email: [resumeContent.emailAddress],
      academyHistories: resumeContent.academy,
      employmentHistories: resumeContent.employment.map((employment) => {
        return {
          companyName: employment.companyName,
          startDate: employment.startDate,
          endDate: employment.endDate,
          currentlyWorking: employment.currentlyWorking,
          description: employment.description,
          workingPositionHistories: [...employment.workingPositionHistory],
        };
      }),
      currentSalary: workPreferenceContent.workPreferenceContent.currentSalary,
      workPreference: workPreferenceContent.workPreferenceContent,
      userTechnicalSkills: resumeContent.technicalSkill,
      personalProject: resumeContent.personalProject.map((personalProject) => {
        return {
          projectName: personalProject.projectName,
          startDate: personalProject.startDate,
          endDate: personalProject.endDate,
          workingPosition: personalProject.workingPosition,
          description: personalProject.description,
        };
      }),
      award: resumeContent.award,
      publication: resumeContent.publication,
      profilePhoto: resumeContent.profilePhoto,
      phoneNumber: resumeContent.phoneNumber,
      preferredName: resumeContent.preferredName,
      channel: resumeContent.channel,
      certification: resumeContent.certification,
      language: resumeContent.language,
    };
    console.log('createProfile', JSON.stringify(createProfile));
  }

  @OnQueueCompleted()
  completed(job: Job) {
    console.log(`Job ${job.id} has completed!`);
  }
}
