import { Injectable, Logger } from '@nestjs/common';
import { Cron, CronExpression } from '@nestjs/schedule';
import { PrismaService } from '../prisma/prisma.service';
import { MongoClient } from 'mongodb';
import { ConfigService } from '@nestjs/config';
import { Prisma } from '@prisma/client';

// Status enum matching MongoDB schema
const STATUS = {
  NEW: 1,
  UPDATED: 2,
  EMBEDDED: 3,
};

// Helper function to remove null fields from an object
function removeNullFields(obj: any): any {
  if (obj === null || obj === undefined) {
    return undefined;
  }

  // Handle Date objects
  if (obj instanceof Date) {
    return obj;
  }

  if (Array.isArray(obj)) {
    const filtered = obj
      .map((item) => removeNullFields(item))
      .filter((item) => item !== undefined);
    return filtered; // Return the array even if empty
  }

  if (typeof obj === 'object') {
    const result: any = {};
    for (const [key, value] of Object.entries(obj)) {
      const cleanedValue = removeNullFields(value);

      if (cleanedValue !== undefined) {
        result[key] = cleanedValue;
      }
    }
    return Object.keys(result).length ? result : undefined;
  }

  return obj;
}

@Injectable()
export class ProfileSyncService {
  private readonly logger = new Logger(ProfileSyncService.name);
  private readonly mongoClient: MongoClient;
  private readonly MILLISECONDS_PER_YEAR = 1000 * 60 * 60 * 24 * 365;
  private readonly MILLISECONDS_PER_MONTH = 1000 * 60 * 60 * 24 * 30;
  constructor(
    private readonly prisma: PrismaService,
    private readonly configService: ConfigService,
  ) {
    this.mongoClient = new MongoClient(
      this.configService.get<string>('MONGODB_URI'),
    );
  }

  @Cron(CronExpression.EVERY_30_SECONDS)
  async syncUnsyncedProfiles() {
    this.logger.log('Starting profile sync to MongoDB...');

    try {
      // First get all unsynchronized ProfileLog entries
      const unsyncedLogs = await this.prisma.profileLog.findMany({
        where: {
          isSyncedToMongo: false,
          isLatest: true,
        },
      });

      if (unsyncedLogs.length === 0) {
        this.logger.log('No unsynchronized profiles found');
        return;
      }

      // Store the logIds we're going to process
      const logIdsToProcess = new Set(
        unsyncedLogs.map((log) => log.profileLogId),
      );

      // Group logs by profileId and get only the latest log for each profile
      const profileLogMap = new Map<number, (typeof unsyncedLogs)[0]>();
      for (const log of unsyncedLogs) {
        const existingLog = profileLogMap.get(log.profileId);
        if (!existingLog || log.changedAt > existingLog.changedAt) {
          profileLogMap.set(log.profileId, log);
        }
      }

      const uniqueProfileIds = Array.from(profileLogMap.keys());

      // Then get the detailed profiles for these logs
      const profiles = await this.prisma.profile.findMany({
        where: {
          profileId: {
            in: uniqueProfileIds,
          },
        },
        include: {
          // --- Direct Relations ---
          company: {
            include: {
              industry: true,
            },
          },
          country: true,
          currency: true,
          bypassSetting: true,

          // --- Nested Relations ---
          userRole: {
            include: {
              user: { select: { email: true } },
              role: { select: { roleName: true } },
            },
          },
          workPreference: {
            include: {
              physicalLocation: {
                include: {
                  country: true,
                  city: true,
                },
              },
            },
          },
          academy: {
            include: {
              degree: true,
              degreeStatus: true,
            },
          },
          employment: {
            include: {
              employmentTechnicalSkill: {
                include: {
                  technicalSkill: true,
                },
              },
              workingPositionHistory: {
                include: {
                  employmentType: true,
                },
              },
            },
          },
          personalProject: true,
          award: true,
          publication: true,
          channel: {
            include: {
              platform: true,
            },
          },
          userTechnicalSkills: {
            include: {
              technicalSkill: true,
            },
          },
          profileManagedByAdmin: {
            include: {
              topTechnicalSkills: { include: { technicalSkill: true } },
              workingPositions: {
                include: { workingPosition: { include: { category: true } } },
              },
            },
          },
        },
      });

      // Filter out profiles with 'deleted' in user email
      const activeProfiles = profiles.filter((profile) => {
        const userEmail = profile.userRole?.user?.email;
        return !userEmail || !userEmail.includes('deleted');
      });

      if (activeProfiles.length === 0) {
        this.logger.log('No active profiles to sync');
        return;
      }

      this.logger.log(`Found ${activeProfiles.length} profiles to sync`);

      // Connect to MongoDB
      await this.mongoClient.connect();
      const db = this.mongoClient.db(this.configService.get<string>('DB_NAME'));
      const collection = db.collection(
        this.configService.get<string>('DB_COLLECTION'),
      );

      // Process each profile
      for (const profile of activeProfiles) {
        try {
          // Check if profile exists in MongoDB
          const existingProfile = await collection.findOne({
            candidateId: profile.profileId,
          });
          const status = existingProfile ? STATUS.UPDATED : STATUS.NEW;

          // If profile is not in candidate catalogue, update isActive to false in MongoDB
          if (!profile.isInCandidateCatalogue) {
            if (existingProfile) {
              await collection.updateOne(
                { candidateId: profile.profileId },
                {
                  $set: {
                    isActive: false,
                    updatedAt: new Date(),
                  },
                },
              );
              this.logger.log(
                `Updated profile ${profile.profileId} - set isActive to false in MongoDB`,
              );

              // Mark the profile logs as synced since we've handled this profile
              await this.prisma.profileLog.updateMany({
                where: {
                  profileLogId: {
                    in: Array.from(logIdsToProcess),
                  },
                  profileId: profile.profileId,
                },
                data: { isSyncedToMongo: true },
              });
            }
            continue;
          }

          const profileTopSkills =
            profile.profileManagedByAdmin?.topTechnicalSkills
              ?.map((item) => item.technicalSkill)
              .filter((skill) => skill != null) || [];

          const totalExperience = profile.employment
            ? this.normalizeExperience(
                this.calculateExperience(
                  profile.employment.map((emp) => ({
                    startDate: emp.startDate,
                    endDate: emp.endDate,
                    currentlyWorking: emp.currentlyWorking,
                  })),
                ),
              )
            : undefined;
          // Transform the profile data to match MongoDB schema and remove null fields
          const transformedProfile = removeNullFields({
            isActive: true,
            isInCandidateCatalogue: profile.isInCandidateCatalogue,
            profileId: profile.profileId,
            userId: profile.userId,
            mainEmail:
              profile.userRole?.user?.email ?? profile.email[0] ?? null,
            //status: profile.status,
            //address: profile.address,
            birthDate: profile.birthDate,
            company: profile.company,
            country: profile.country,
            currency: profile.currency,
            workPreference: profile.workPreference,
            academy: profile.academy,
            employment: profile.employment,
            personalProject: profile.personalProject,
            award: profile.award,
            publication: profile.publication,
            channel: profile.channel,
            userTechnicalSkills: profile.userTechnicalSkills,
            bypassSetting: profile.bypassSetting,
            firstName: profile.firstName,
            lastName: profile.lastName,
            preferredName: profile.preferredName,
            middleName: profile.middleName,
            //phoneNumber: profile.phoneNumber,
            profilePhoto: profile.profilePhoto,
            socialLink: profile.socialLink,
            //cv: profile.cv,
            resumeSummary: profile.resumeSummary,
            currentSalary: profile.currentSalary,
            expectedSalary: profile.expectedSalary,
            minSalary: profile.minSalary,
            //subcontractorRate: profile.subcontractorRate,
            monthlyRate: profile.monthlyRate,
            hourlyRate: profile.hourlyRate,
            seniority: profile.seniority,
          });

          const updatedProfile = {
            profileTopSkills,
            totalExperience,
            ...transformedProfile,
          };
          console.log('Profile', updatedProfile);

          // Upsert the data in MongoDB, but only update specific fields
          await collection.updateOne(
            { candidateId: profile.profileId },
            {
              $set: {
                payload: updatedProfile,
                candidateName:
                  `${profile.firstName || ''} ${profile.lastName || ''}`.trim(),
                status: status,
                rawText: JSON.stringify(updatedProfile),
                isActive: true,
                updatedAt: new Date(),
              },
              $setOnInsert: {
                embedding: null,
                label: 'all',
                createdAt: new Date(),
              },
            },
            { upsert: true },
          );

          // Update only the logs that were present at the start of the sync
          await this.prisma.profileLog.updateMany({
            where: {
              profileLogId: {
                in: Array.from(logIdsToProcess),
              },
              profileId: profile.profileId,
            },
            data: { isSyncedToMongo: true },
          });

          this.logger.log(
            `Successfully synced profile ${profile.profileId} to MongoDB with status ${status}`,
          );
        } catch (error) {
          this.logger.error(
            `Error syncing profile ${profile.profileId}: ${error.message}`,
          );
        }
      }
    } catch (error) {
      this.logger.error(`Error in sync process: ${error.message}`);
    } finally {
      await this.mongoClient.close();
    }
  }

  private calculateExperience(
    employment: Prisma.EmploymentHistoryGetPayload<{
      select: {
        startDate: true;
        endDate: true;
        currentlyWorking: true;
      };
    }>[],
  ): {
    years: number;
    months: number;
  } {
    // Sort employment periods by start date
    const sortedPeriods = [...employment].sort(
      (a, b) =>
        new Date(a.startDate).getTime() - new Date(b.startDate).getTime(),
    );

    // Merge overlapping periods
    const mergedPeriods: { start: Date; end: Date }[] = [];

    for (const period of sortedPeriods) {
      const start = new Date(period.startDate);
      const end = period.currentlyWorking
        ? new Date()
        : new Date(period.endDate);

      if (mergedPeriods.length === 0) {
        mergedPeriods.push({ start, end });
        continue;
      }

      const lastPeriod = mergedPeriods[mergedPeriods.length - 1];

      // If current period overlaps with last period, merge them
      if (start <= lastPeriod.end) {
        lastPeriod.end = end > lastPeriod.end ? end : lastPeriod.end;
      } else {
        mergedPeriods.push({ start, end });
      }
    }

    // Calculate total experience from merged periods
    return mergedPeriods.reduce(
      (total, period) => {
        const diff = period.end.getTime() - period.start.getTime();
        const years = Math.floor(diff / this.MILLISECONDS_PER_YEAR);
        const months = Math.floor(
          (diff % this.MILLISECONDS_PER_YEAR) / this.MILLISECONDS_PER_MONTH,
        );
        return {
          years: total.years + years,
          months: total.months + months,
        };
      },
      { years: 0, months: 0 },
    );
  }

  private normalizeExperience(experience: { years: number; months: number }) {
    return {
      years: experience.years + Math.floor(experience.months / 12),
      months: experience.months % 12,
    };
  }
}
