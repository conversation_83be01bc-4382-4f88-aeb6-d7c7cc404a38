import { Injectable } from '@nestjs/common';
import { InjectQueue } from '@nestjs/bull';
import { Queue } from 'bull';
import { Interval } from '@nestjs/schedule';
import { AIBotTestRoomStatus, HandleScoringStatus } from '@prisma/client';

import {
  IHandleScoringService,
  IHandleScoring,
} from 'src/utils/interfaces/interview.interface';
import { QueueName, InteviewTask } from 'src/utils/enums';
import { PrismaService } from '../prisma/prisma.service';

@Injectable()
export class HandleScoringService implements IHandleScoringService {
  constructor(
    @InjectQueue(QueueName.SCORING_HANDLING_QUEUE)
    private scoringHandleQueue: Queue<IHandleScoring>,
    private prismaService: PrismaService,
  ) { }

  @Interval(60 * 1000)
  async scanAndStore(): Promise<void> {
    console.log('Start scanning and storing rooms...');
    const rooms = await this.prismaService.aIBotTestRoom.findMany({
      where: {
        roomStatus: AIBotTestRoomStatus.INTERVIEW_COMPLETED,
        OR: [
          { handleScoringStatus: HandleScoringStatus.PENDING },
          { handleScoringStatus: HandleScoringStatus.FAILED },
          { handleScoringStatus: null },
        ],
      },
      orderBy: { createdAt: 'asc' },
      include: {
        aiBotTestSection: true,
      },
    });
    for (const room of rooms) {
      // Update status 
      await this.prismaService.aIBotTestRoom.update({
        where: {
          aIBotTestRoomId: room.aIBotTestRoomId,
        },
        data: {
          handleScoringStatus: HandleScoringStatus.PROCESSING,
        },
      });
      this.scoringHandleQueue.add(
        InteviewTask.SCORING_HANDLING_TASK,
        { room },
        {
          jobId: `scoringHandling-${room.aIBotTestRoomId}-${new Date().toISOString()}`,
        },
      );
    }
  }
}
