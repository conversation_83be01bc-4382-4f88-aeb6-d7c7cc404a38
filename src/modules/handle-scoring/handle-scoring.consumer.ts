import { Processor, Process, OnQueueCompleted } from '@nestjs/bull';
import { Job } from 'bull';
import axios from 'axios';
import { AIBotTestRoom, HandleScoringStatus } from '@prisma/client';

import { QueueName, InteviewTask } from 'src/utils/enums';
import {
  IHandleScoring,
  IHandleScoringConsumer,
  MeetingScoringInput,
  MeetingTranscription,
} from 'src/utils/interfaces/interview.interface';
import { PrismaService } from '../prisma/prisma.service';

@Processor(QueueName.SCORING_HANDLING_QUEUE)
export class HandleScoringConsumer implements IHandleScoringConsumer {
  private readonly AI_GENERATE_URL = process.env.AI_BOT_URL;

  constructor(private readonly prismaService: PrismaService) { }

  async getTranscriptions(
    room: AIBotTestRoom,
    email: string
  ): Promise<MeetingTranscription[]> {
    try {
      const response = await axios.get(
        `${this.AI_GENERATE_URL}/meeting/transcriptions`,
        {
          params: {
            'room-url': room.roomUrl,
            email: email,
          },
          headers: {
            Accept: 'application/json',
          },
        },
      );

      if (response.data?.code === 200 && Array.isArray(response.data.data)) {
        return response.data.data as MeetingTranscription[];
      }

      console.warn('Unexpected response format from AI generate service');
      return [];
    } catch (error: any) {
      console.error(
        'Error calling AI generate service:',
        error?.response?.data || error.message,
      );
      return [];
    }
  }

  async getScoring(
    room: AIBotTestRoom,
    email: string,
  ): Promise<MeetingScoringInput | null> {
    try {
      const response = await axios.get(
        `${this.AI_GENERATE_URL}/llm/get-scoring`,
        {
          params: {
            'room-url': room.roomUrl,
            email: email,
          },
          headers: {
            Accept: 'application/json',
          },
        },
      );

      const data = response.data?.data;
      if (response.data?.code !== 200 || !data) {
        console.warn('Unexpected response format from scoring API');
        return null;
      }

      const scoringModel: MeetingScoringInput = {
        aIBotTestRoomId: room.aIBotTestRoomId,
        fluencyAndCoherence: data.cefr_standard?.fluency_and_coherence,
        lexicalResource: data.cefr_standard?.lexical_resource,
        grammaticalRangeAndAccuracy:
          data.cefr_standard?.grammatical_range_and_accuracy,
        CEFRStandard: data.cefr_standard?.CEFR_standard,
        feedbacks: (data.feedbacks ?? []).map((f: any) => ({
          aiAssessment: f.ai_assessment,
          scoringLevel: f.scoring_level,
          question: f.question,
          idealAnswer: f.ideal_answer,
          userAnswer: f.user_answer,
        })),
      };

      return scoringModel;
    } catch (error: any) {
      console.error(
        'Error calling get-scoring API:',
        error?.response?.data || error.message,
      );
      return null;
    }
  }

  @Process({
    name: InteviewTask.SCORING_HANDLING_TASK,
    concurrency: 3,
  })
  async processRoom(job: Job<IHandleScoring>): Promise<void> {
    const roomId = job.data.room.aIBotTestRoomId;

    try {
      console.log('Start processing room', roomId);

      let email = '<EMAIL>';

      if (job.data.room.aiBotTestSectionId) {
        const section = await this.prismaService.aIBotTestSection.findFirst({
          where: { aiBotTestSectionId: job.data.room.aiBotTestSectionId },
        });

        if (section?.masterFormEmail) {
          email = section.masterFormEmail;
        }
      }

      const transcriptions = await this.getTranscriptions(job.data.room, email);
      if (transcriptions.length) {
        for (const t of transcriptions) {
          await this.prismaService.meetingTranscription.create({
            data: {
              role: t.role,
              content: t.content,
              createdAt: new Date(t.created_at),
              aIBotTestRoom: { connect: { aIBotTestRoomId: roomId } },
            },
          });
        }
      }

      const scoring = await this.getScoring(job.data.room, email);
      if (scoring) {
        await this.prismaService.meetingScoring.create({
          data: {
            fluencyAndCoherence: scoring.fluencyAndCoherence,
            lexicalResource: scoring.lexicalResource,
            grammaticalRangeAndAccuracy: scoring.grammaticalRangeAndAccuracy,
            CEFRStandard: scoring.CEFRStandard,
            aIBotTestRoom: {
              connect: { aIBotTestRoomId: scoring.aIBotTestRoomId },
            },
            feedbacks: {
              create: scoring.feedbacks.map((f) => ({
                aiAssessment: f.aiAssessment,
                scoringLevel: f.scoringLevel,
                question: f.question,
                idealAnswer: f.idealAnswer,
                userAnswer: f.userAnswer,
              })),
            },
          },
        });
      }

      await this.prismaService.aIBotTestRoom.update({
        where: { aIBotTestRoomId: roomId },
        data: {
          handleScoringStatus:
            !email || (!scoring && transcriptions.length === 0)
              ? HandleScoringStatus.FAILED
              : HandleScoringStatus.DONE,
        },
      });
    } catch (error) {
      console.error(
        `Error processing room ${job.data.room.aIBotTestRoomId}`,
        error,
      );
      await this.prismaService.aIBotTestRoom.update({
        where: { aIBotTestRoomId: job.data.room.aIBotTestRoomId },
        data: {
          handleScoringStatus: HandleScoringStatus.FAILED,
        },
      });
    }
  }


  @OnQueueCompleted()
  completed(job: Job) {
    console.log(`Job ${job.id} has completed!`);
  }
}
