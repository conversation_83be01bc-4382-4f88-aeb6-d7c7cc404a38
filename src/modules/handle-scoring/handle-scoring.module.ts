import { Modu<PERSON> } from '@nestjs/common';
import { BullModule } from '@nestjs/bull';
import { PrismaModule } from '../prisma/prisma.module';

import { HandleScoringService } from './handle-scoring.service';
import { HandleScoringController } from './handle-scoring.controller';
import { HandleScoringConsumer } from './handle-scoring.consumer';
import { QueueName } from 'src/utils/enums';

@Module({
  imports: [
    BullModule.registerQueue({
      name: QueueName.SCORING_HANDLING_QUEUE,
    }),
    PrismaModule,
  ],
  controllers: [HandleScoringController],
  providers: [HandleScoringService, HandleScoringConsumer],
  exports: [HandleScoringService],
})
export class HandleScoringModule {}
