import { Processor, Process, OnQueueCompleted } from '@nestjs/bull';
import { Job } from 'bull';
import axios from 'axios';

import { QueueName, TestScoringTask } from './../../utils/enums';
import {
  ITaskTestScoringConsumer,
  ITestScoring,
} from './../../utils/interfaces/test-scoring.interface';
import { PrismaService } from '../prisma/prisma.service';
import {
  TestStatus,
  ApplicationStatus,
  EnglishScoringMlResponse,
} from './../../utils/enums';
import {
  EnglishScoringResponse,
  EnglishAudioScoringResponse,
  TechnicalScoringResponse,
  AiBotScoringResponse,
} from 'src/utils/types/test-scoring.type';

@Processor(QueueName.TEST_SCORING_QUEUE)
export class TaskTestScoringConsumer implements ITaskTestScoringConsumer {
  constructor(private readonly prismaService: PrismaService) {}
  private readonly SCORE_TO_PASS = 50;
  private readonly ML_URL = process.env.ML_URL;
  private readonly ENGLISH_TEST_SCORING_URL = `${this.ML_URL}/api/tts/mp3-scoring`;
  private readonly ENGLISH_TEST_SCORING_V2_URL = `${this.ML_URL}/api/tts/internal-mp3-scoring-v2`;
  private readonly AI_BOT_TEST_SCORING_URL = `${this.ML_URL}/api/rag/conversation-scoring/score`;
  private readonly ENGLISH_TEST_AUDIO_SCORING_URL = `${this.ML_URL}/api/tts/internal-mp3-scoring`;
  private readonly TECHNICAL_TEST_SCORING_URL = `${this.ML_URL}/api/rag/score-answer`;

  @Process({ name: TestScoringTask.ENGLISH_TEST_SCORING_TASK, concurrency: 1 })
  async processEnglishTestScoring(job: Job<ITestScoring>): Promise<void> {
    console.log('Start processing job...', job.data);
    const retries = 20;
    const scoreRes = await axios.post<EnglishScoringResponse>(
      this.ENGLISH_TEST_SCORING_V2_URL,
      {
        // topic: job.data.englishTest.question,
        mp3_url: job.data.englishTest.userAnswer,
      },
      { timeout: 10 * 1000 },
    );

    for (let i = 0; i < retries; i++) {
      const audioScoreRes = await axios.get<EnglishAudioScoringResponse>(
        `${this.ENGLISH_TEST_SCORING_V2_URL}?record_id=${scoreRes.data.data.record_id}`,
        { timeout: 10 * 1000 },
      );
      console.log('Audio score response:', audioScoreRes.data.data);
      if (audioScoreRes.data.data.result) {
        const score = audioScoreRes.data.data.result.overall_score;
        const result = audioScoreRes.data.data.result.overall_result;
        let status: TestStatus = TestStatus.GREY_ZONE;

        if (result === EnglishScoringMlResponse.FAIL)
          status = TestStatus.TEST_FAILED;
        if (result === EnglishScoringMlResponse.PASS)
          status = TestStatus.TEST_PASSED;

        console.log('Score response:', {
          ...audioScoreRes.data.data.result.summary,
          ...audioScoreRes.data.data.result.content_score,
          overall_score: score,
          overall_result: result,
        });

        await this.prismaService.englishTest.update({
          where: { englishTestId: job.data.englishTest.englishTestId },
          data: {
            score,
            status,
          },
        });
        break;
      }

      await new Promise((resolve) => setTimeout(resolve, 5 * 60 * 1000));
    }
  }

  @Process({
    name: TestScoringTask.TECHNICAL_TEST_SCORING_TASK,
    concurrency: 1,
  })
  async processTechnicalTestScoring(job: Job<ITestScoring>): Promise<void> {
    console.log('Start processing job...', job.data);
    // Each ans will score in 100 points, if >= 50 then final score will be plus 10, if final score >= 50 then pass
    let finalScore = 0;
    const technicalQuestions = job.data.technicalTest.technicalTestQuestions;
    for (const technicalQuestion of technicalQuestions) {
      let score = 0;
      if (technicalQuestion.answer) {
        const scoreRes = await axios.post<TechnicalScoringResponse>(
          this.TECHNICAL_TEST_SCORING_URL,
          {
            question: technicalQuestion.question.question,
            answer: technicalQuestion.answer,
          },
        );
        score = parseInt(scoreRes.data.data.score, 10);
        console.log('Question:', technicalQuestion.question.question);
        console.log('Score response:', scoreRes.data.data.score);
      }
      if (score >= this.SCORE_TO_PASS) finalScore += 10;
      await this.prismaService.technicalTestQuestion.update({
        where: {
          technicalTestQuestionId: technicalQuestion.technicalTestQuestionId,
        },
        data: {
          score,
        },
      });
    }

    await this.prismaService.technicalTest.update({
      where: { technicalTestId: job.data.technicalTest.technicalTestId },
      data: {
        score: finalScore,
        status:
          finalScore >= this.SCORE_TO_PASS
            ? TestStatus.TEST_PASSED
            : TestStatus.TEST_FAILED,
      },
    });
  }

  @Process({
    name: TestScoringTask.AI_BOT_TEST_SCORING_TASK,
    concurrency: 1,
  })
  async processAIBotTestScoring(job: Job<ITestScoring>): Promise<void> {
    console.log('Start processing job...', job.data);
    const retries = 20;
    const firstMessageInConversation =
      await this.prismaService.aIBotTestQuestion.findFirst({
        where: {
          aiBotTestId: job.data.aiBotTest.aiBotTestId,
          messageId: {
            not: null,
          },
        },
      });
    // add this message to english test for scoring
    await this.prismaService.englishTest.update({
      where: { foundationTestId: job.data.aiBotTest.foundationTestId },
      data: {
        userAnswer: firstMessageInConversation.audioRecordUrl,
        status: TestStatus.TEST_COMPLETED,
      },
    });

    for (let i = 0; i < retries; i++) {
      const scoreRes = await axios.post<AiBotScoringResponse>(
        this.AI_BOT_TEST_SCORING_URL,
        {
          conversation_id: job.data.aiBotTest.conversationId,
        },
        { timeout: 1 * 60 * 1000 },
      );

      if (scoreRes.data.data) {
        let totalScore: number = 0;
        for (const score of scoreRes.data.data) {
          await this.prismaService.aIBotTestQuestion.create({
            data: {
              aiBotTestId: job.data.aiBotTest.aiBotTestId,
              question: score.question,
              answer: score.answer,
              score: score.score,
            },
          });
          totalScore += score.score;
        }

        // Average score
        totalScore /= scoreRes.data.data.length;
        await this.prismaService.aIBotTest.update({
          where: { aiBotTestId: job.data.aiBotTest.aiBotTestId },
          data: {
            score: totalScore,
            status:
              totalScore >= this.SCORE_TO_PASS
                ? TestStatus.TEST_PASSED
                : TestStatus.TEST_FAILED,
          },
        });
        break;
      }
      await new Promise((resolve) => setTimeout(resolve, 2 * 60 * 1000));
    }
  }

  @Process({
    name: TestScoringTask.CODING_TEST_SCORING_TASK,
    concurrency: 1,
  })
  async processCodingTestScoring(job: Job<ITestScoring>): Promise<void> {
    console.log('Start processing job...', job.data);
    const { codingTest } = job.data;
    await this.prismaService.codingTest.update({
      where: { codingTestId: job.data.codingTest.codingTestId },
      data: {
        status:
          codingTest.question[0].score >= 5
            ? TestStatus.TEST_PASSED
            : TestStatus.TEST_FAILED,
        score: codingTest.question[0].score,
      },
    });
  }

  @Process({ name: TestScoringTask.TEST_RESULT_TASK, concurrency: 1 })
  async processTestResult(job: Job<ITestScoring>): Promise<void> {
    console.log('Start processing job...', job.data);
    // Foundation test result
    let status = TestStatus.TEST_PASSED;
    if (
      job.data.testResult.technicalTest.status === TestStatus.TEST_FAILED ||
      job.data.testResult.englishTest.status === TestStatus.TEST_FAILED ||
      job.data.testResult.codingTest.status === TestStatus.TEST_FAILED ||
      job.data.testResult.aiBotTest.status === TestStatus.TEST_FAILED
    )
      status = TestStatus.TEST_FAILED;
    await this.prismaService.foundationTest.update({
      where: { foundationTestId: job.data.testResult.foundationTestId },
      data: {
        status,
      },
    });
    if (status === TestStatus.TEST_FAILED)
      await this.prismaService.application.update({
        where: { applicationId: job.data.testResult.applicationId },
        data: {
          status: ApplicationStatus.APPLICATION_REJECTED,
        },
      });
  }

  @OnQueueCompleted()
  completed(job: Job) {
    console.log(`Job ${job.id} has completed!`);
  }
}
