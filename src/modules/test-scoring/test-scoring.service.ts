import { Injectable } from '@nestjs/common';
import { InjectQueue } from '@nestjs/bull';
import { Queue } from 'bull';
import { Interval } from '@nestjs/schedule';

import {
  ITestScoringService,
  ITestScoring,
} from 'src/utils/interfaces/test-scoring.interface';
import { PrismaService } from '../prisma/prisma.service';
import { TestStatus, QueueName, TestScoringTask } from 'src/utils/enums';

@Injectable()
export class TestScoringService implements ITestScoringService {
  constructor(
    private readonly prismaService: PrismaService,
    @InjectQueue(QueueName.TEST_SCORING_QUEUE)
    private testScoringQueue: Queue<ITestScoring>,
  ) {}

  // @Interval(1 * 60 * 1000)
  // async englishTestScoring(): Promise<void> {
  //   const englishTests = await this.prismaService.englishTest.findMany({
  //     where: { status: TestStatus.TEST_COMPLETED },
  //     orderBy: { createdAt: 'asc' },
  //   });
  //   for (const englishTest of englishTests) {
  //     this.testScoringQueue.add(
  //       TestScoringTask.ENGLISH_TEST_SCORING_TASK,
  //       { englishTest: englishTest },
  //       {
  //         jobId: `englishTest-${englishTest.englishTestId}`,
  //       },
  //     );
  //   }
  // }

  // @Interval(1 * 60 * 1000)
  // async technicalTestScoring(): Promise<void> {
  //   const technicalTests = await this.prismaService.technicalTest.findMany({
  //     where: { status: TestStatus.TEST_COMPLETED },
  //     orderBy: { createdAt: 'asc' },
  //   });

  //   for (const technicalTest of technicalTests) {
  //     const technicalTestQuestions =
  //       await this.prismaService.technicalTestQuestion.findMany({
  //         where: { technicalTestId: technicalTest.technicalTestId },
  //         include: { question: true },
  //       });
  //     this.testScoringQueue.add(
  //       TestScoringTask.TECHNICAL_TEST_SCORING_TASK,
  //       { technicalTest: { ...technicalTest, technicalTestQuestions } },
  //       {
  //         jobId: `technicalTest-${technicalTest.technicalTestId}`,
  //       },
  //     );
  //   }
  // }

  // @Interval(1 * 60 * 1000)
  // async aiBotTestScoring(): Promise<void> {
  //   const aiBotTests = await this.prismaService.aIBotTest.findMany({
  //     where: { status: TestStatus.TEST_COMPLETED },
  //     orderBy: { createdAt: 'asc' },
  //   });

  //   for (const aiBotTest of aiBotTests) {
  //     this.testScoringQueue.add(
  //       TestScoringTask.AI_BOT_TEST_SCORING_TASK,
  //       { aiBotTest: aiBotTest },
  //       {
  //         jobId: `aiBotTest-${aiBotTest.aiBotTestId}`,
  //       },
  //     );
  //   }
  // }

  // @Interval(1 * 60 * 1000)
  // async codingTestScoring(): Promise<void> {
  //   const codingTests = await this.prismaService.codingTest.findMany({
  //     where: { status: TestStatus.TEST_COMPLETED },
  //     orderBy: { createdAt: 'asc' },
  //     include: { question: true },
  //   });

  //   for (const codingTest of codingTests) {
  //     this.testScoringQueue.add(
  //       TestScoringTask.CODING_TEST_SCORING_TASK,
  //       { codingTest: codingTest },
  //       {
  //         jobId: `codingTest-${codingTest.codingTestId}`,
  //       },
  //     );
  //   }
  // }

  // @Interval(1 * 60 * 1000)
  // async testResult(): Promise<void> {
  //   const foundationTests = await this.prismaService.foundationTest.findMany({
  //     where: {
  //       status: TestStatus.TEST_COMPLETED,
  //       technicalTest: {
  //         status: {
  //           in: [TestStatus.TEST_PASSED, TestStatus.TEST_FAILED],
  //         },
  //       },
  //       englishTest: {
  //         status: {
  //           in: [TestStatus.TEST_PASSED, TestStatus.TEST_FAILED],
  //         },
  //       },
  //       codingTest: {
  //         status: {
  //           in: [TestStatus.TEST_PASSED, TestStatus.TEST_FAILED],
  //         },
  //       },
  //       aiBotTest: {
  //         status: {
  //           in: [TestStatus.TEST_PASSED, TestStatus.TEST_FAILED],
  //         },
  //       },
  //     },
  //     orderBy: { createdAt: 'asc' },
  //     include: {
  //       technicalTest: true,
  //       englishTest: true,
  //       codingTest: true,
  //       aiBotTest: true,
  //     },
  //   });

  //   for (const foundationTest of foundationTests) {
  //     this.testScoringQueue.add(
  //       TestScoringTask.TEST_RESULT_TASK,
  //       { testResult: foundationTest },
  //       {
  //         jobId: `testResult-${foundationTest.foundationTestId}`,
  //       },
  //     );
  //   }
  // }
}
