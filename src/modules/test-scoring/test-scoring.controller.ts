import { Controller, Get, Param } from '@nestjs/common';

import { TestScoringService } from './test-scoring.service';
import { PrismaService } from '../prisma/prisma.service';

@Controller('test-scoring')
export class TestScoringController {
  constructor(
    private readonly testScoringService: TestScoringService,
    private readonly prismaService: PrismaService,
  ) {}

  @Get(':id')
  async getTestScoringResult(@Param('id') id: string): Promise<any> {
    return await this.prismaService.foundationTest.findFirst({
      where: { foundationTestId: Number(id) },
      include: {
        technicalTest: true,
        englishTest: true,
        aiBotTest: true,
        codingTest: true,
      },
    });
  }
}
