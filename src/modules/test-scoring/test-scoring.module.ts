import { <PERSON><PERSON><PERSON> } from '@nestjs/common';
import { BullModule } from '@nestjs/bull';

import { TestScoringService } from './test-scoring.service';
import { TestScoringController } from './test-scoring.controller';
import { TaskTestScoringConsumer } from './test-scoring.consumer';
import { PrismaModule } from '../prisma/prisma.module';
import { QueueName } from '../../utils/enums';

@Module({
  imports: [
    PrismaModule,
    BullModule.registerQueue({
      name: QueueName.TEST_SCORING_QUEUE,
    }),
  ],
  providers: [TestScoringService, TaskTestScoringConsumer],
  controllers: [TestScoringController],
  exports: [TestScoringService],
})
export class TestScoringModule {}
