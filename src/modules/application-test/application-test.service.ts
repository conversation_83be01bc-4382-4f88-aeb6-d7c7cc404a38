import { Injectable } from '@nestjs/common';
import { InjectQueue } from '@nestjs/bull';
import { Queue } from 'bull';
import { Interval } from '@nestjs/schedule';

import {
  IApplicationTestService,
  IApplicationTest,
} from 'src/utils/interfaces/application-test.interface';
import { QueueName, TestTask } from 'src/utils/enums';
import { PrismaService } from '../prisma/prisma.service';
import { ApplicationStatus } from 'src/utils/enums';

@Injectable()
export class ApplicationTestService implements IApplicationTestService {
  private readonly ML_URL = process.env.ML_URL;
  constructor(
    private prismaService: PrismaService,
    @InjectQueue(QueueName.APPLICATION_TEST_QUEUE)
    private applicationTestQueue: Queue<IApplicationTest>,
  ) {}

  // @Interval(1 * 60 * 1000)
  // async applicationTest(): Promise<void> {
  //   const applications = await this.prismaService.application.findMany({
  //     where: {
  //       status: ApplicationStatus.APPLICATION_PENDING,
  //       foundationTest: {
  //         is: null,
  //       },
  //     },
  //     orderBy: {
  //       createdAt: 'asc',
  //     },
  //     include: {
  //       job: { include: { jobSkillLevel: { include: { skillLevel: true } } } },
  //       userRole: { include: { user: true, profile: true } },
  //     },
  //   });
  //   for (const application of applications) {
  //     this.applicationTestQueue.add(
  //       TestTask.APPLICATION_TEST_TASK,
  //       { application: application },
  //       {
  //         jobId: application.applicationId,
  //       },
  //     );
  //   }
  // }

  // @Interval(1 * 60 * 1000)
  // async applicationTestV2(): Promise<void> {
  // const applications = await this.prismaService.application.findMany({
  //   where: {
  //     status: ApplicationStatus.APPLICATION_PENDING,
  //   },
  //   orderBy: {
  //     createdAt: 'asc',
  //   },
  //   include: {
  //     foundationTest: { include: { technicalTest: true, englishTest: true } },
  //     job: { include: { jobSkillLevel: { include: { skillLevel: true } } } },
  //     userRole: { include: { user: true, profile: true } },
  //   },
  // });
  // for (const application of applications) {
  //   if (!application.foundationTest.technicalTest)
  //     this.applicationTestQueue.add(
  //       TestTask.APPLICATION_TEST_V2_TASK,
  //       { application: application },
  //       {
  //         jobId: application.applicationId,
  //       },
  //     );
  // }
  // }
}
