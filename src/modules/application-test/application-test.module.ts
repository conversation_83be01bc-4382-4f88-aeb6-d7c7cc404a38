import { Modu<PERSON> } from '@nestjs/common';
import { BullModule } from '@nestjs/bull';

import { ApplicationTestService } from './application-test.service';
import { PrismaModule } from '../prisma/prisma.module';
import { ApplicationTestController } from './application-test.controller';
import { TaskApplicationTestConsumer } from './application-test.consumer';
import { QueueName } from 'src/utils/enums';

@Module({
  imports: [
    PrismaModule,
    BullModule.registerQueue({
      name: QueueName.APPLICATION_TEST_QUEUE,
    }),
  ],
  providers: [ApplicationTestService, TaskApplicationTestConsumer],
  controllers: [ApplicationTestController],
})
export class ApplicationTestModule {}
