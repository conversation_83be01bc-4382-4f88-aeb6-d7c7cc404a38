import { Processor, Process, OnQueueCompleted } from '@nestjs/bull';
import { Job } from 'bull';
import axios from 'axios';

import {
  QueueName,
  TestTask,
  ExperienceLevel,
  CodingChallengeLevel,
  TestStatus,
} from './../../utils/enums';
import {
  ITaskApplicationTestConsumer,
  IApplicationTest,
} from './../../utils/interfaces/application-test.interface';
import {
  JobSkillsParam,
  TechniqueQuestionsParam,
  JobSkillsResponse,
  TechniqueQuestionsResponse,
  CreateConversationResponse,
  JobSkills,
  CodingChallengeQuestionsParam,
} from 'src/utils/types/application-test.type';
import { PrismaService } from '../prisma/prisma.service';
import { ENGLISH_QUESTION } from 'src/utils/constants/application-test.constants';
import { JobSkillNotFoundException } from 'src/exceptions';

@Processor(QueueName.APPLICATION_TEST_QUEUE)
export class TaskApplicationTestConsumer
  implements ITaskApplicationTestConsumer
{
  constructor(private prismaService: PrismaService) {}
  private readonly ML_URL = process.env.ML_URL;
  private readonly NUM_OF_TECHNIQUE_QUESTIONS = 10;
  private readonly GET_JOB_SKILLS_URL = `${this.ML_URL}/api/rag/profile-analysis/extract-skills-categories`;
  private readonly CREATE_CONVERSATION_URL = `${this.ML_URL}/api/rag/user-conversation/start`;

  async getJobSkills(
    jobSkillsParam: JobSkillsParam,
  ): Promise<JobSkillsResponse> {
    console.log(jobSkillsParam.jobDescription);
    const jobSkillsResponse = await axios.post<JobSkillsResponse>(
      this.GET_JOB_SKILLS_URL,
      { text: jobSkillsParam.jobDescription },
    );

    return jobSkillsResponse.data;
  }

  QAQCJobSkills(JobSkills: JobSkills): JobSkills {
    for (const jobSkill of JobSkills) {
      if (
        jobSkill.skillName === 'QA/QC' ||
        jobSkill.skillName === 'Software Testing'
      ) {
        return [
          {
            skillName: 'Software Testing',
            id: '85',
          },
          {
            skillName: 'Java',
            id: '162',
          },
        ];
      }
    }

    return JobSkills;
  }

  isExsitingQuestion(
    techniqueQuestions: TechniqueQuestionsResponse[],
    id: number,
  ): boolean {
    for (const techniqueQuestion of techniqueQuestions) {
      if (techniqueQuestion.interviewQuestionId === id) return true;
    }

    return false;
  }

  async getTechniqueQuestions(
    techniqueQuestionsParam: TechniqueQuestionsParam,
  ): Promise<TechniqueQuestionsResponse[]> {
    // job entry level: 5 easy, 3 medium, 2 hard
    // job junior or mid level: 4 easy, 4 medium, 2 hard
    // job senior level: 3 easy, 4 medium, 3 hard
    // job lead or expert level: 2 easy, 3 medium, 5 hard
    let temp = 0;
    const jobSkillsLength = techniqueQuestionsParam.jobSkills.length;
    const techniqueQuestions: TechniqueQuestionsResponse[] = [];
    const levels = {
      Entry: [
        { min: 0, max: 6, level: ExperienceLevel.ENTRY },
        { min: 6, max: 8, level: ExperienceLevel.JUNIOR },
        { min: 8, max: 10, level: ExperienceLevel.SENIOR },
      ],
      Intern: [
        { min: 0, max: 5, level: ExperienceLevel.ENTRY },
        { min: 5, max: 8, level: ExperienceLevel.JUNIOR },
        { min: 8, max: 10, level: ExperienceLevel.SENIOR },
      ],
      Fresher: [
        { min: 0, max: 4, level: ExperienceLevel.ENTRY },
        { min: 4, max: 8, level: ExperienceLevel.JUNIOR },
        { min: 8, max: 10, level: ExperienceLevel.SENIOR },
      ],
      Junior: [
        { min: 0, max: 1, level: ExperienceLevel.ENTRY },
        { min: 1, max: 8, level: ExperienceLevel.JUNIOR },
        { min: 8, max: 10, level: ExperienceLevel.SENIOR },
      ],
      Middle: [
        { min: 0, max: 1, level: ExperienceLevel.ENTRY },
        { min: 1, max: 8, level: ExperienceLevel.JUNIOR },
        { min: 8, max: 10, level: ExperienceLevel.SENIOR },
      ],
      Senior: [
        { min: 0, max: 1, level: ExperienceLevel.ENTRY },
        { min: 1, max: 5, level: ExperienceLevel.JUNIOR },
        { min: 5, max: 10, level: ExperienceLevel.SENIOR },
      ],
    };
    while (techniqueQuestions.length < this.NUM_OF_TECHNIQUE_QUESTIONS) {
      for (const { min, max, level } of levels[
        techniqueQuestionsParam.jobExperienceLevel
      ]) {
        if (
          techniqueQuestions.length >= min &&
          techniqueQuestions.length < max
        ) {
          const questions = await this.prismaService.interviewQuestion.findMany(
            {
              where: {
                level: level,
                skill: techniqueQuestionsParam.jobSkills[temp].skillName,
              },
            },
          );

          for (const q of questions) {
            if (
              !this.isExsitingQuestion(
                techniqueQuestions,
                q.interviewQuestionId,
              )
            ) {
              techniqueQuestions.push(q);
              break;
            }
          }
        }
      }

      if (temp == jobSkillsLength - 1) {
        temp = 0;
        continue;
      }
      temp++;
    }

    return techniqueQuestions;
  }
  async getCodingChallengeQuestions(
    codingChallengeQuestionsParam: CodingChallengeQuestionsParam,
  ): Promise<TechniqueQuestionsResponse> {
    const levels = {
      Entry: [
        CodingChallengeLevel.EASY,
        CodingChallengeLevel.MEDIUM,
        CodingChallengeLevel.HARD,
      ],
      Intern: [
        CodingChallengeLevel.EASY,
        CodingChallengeLevel.MEDIUM,
        CodingChallengeLevel.HARD,
      ],
      Fresher: [
        CodingChallengeLevel.EASY,
        CodingChallengeLevel.MEDIUM,
        CodingChallengeLevel.HARD,
      ],
      Junior: [CodingChallengeLevel.MEDIUM, CodingChallengeLevel.EASY],
      Middle: [CodingChallengeLevel.MEDIUM, CodingChallengeLevel.EASY],
      Senior: [
        CodingChallengeLevel.HARD,
        CodingChallengeLevel.MEDIUM,
        CodingChallengeLevel.EASY,
      ],
    };

    for (const level of levels[
      codingChallengeQuestionsParam.jobExperienceLevel
    ]) {
      const question = await this.prismaService.interviewQuestion.findFirst({
        where: {
          level: level,
        },
      });

      if (question) return question;
    }

    return null;
  }

  @Process({ name: TestTask.APPLICATION_TEST_TASK, concurrency: 1 })
  async processApplicationTest(job: Job<IApplicationTest>): Promise<void> {
    console.log('Start processing job...', job.data.application.job);
    // Test validation

    // Get job skills
    const jobSkillsResponse: JobSkills = [];
    const QAQCJobSkillsResponseFilter: JobSkills = [];
    const jobExperienceLevel =
      job.data.application.job.jobSkillLevel[0].skillLevel.name;
    const jobSkillsDescriptionResponse = await this.getJobSkills({
      jobDescription: job.data.application.job.description,
    });
    const jobSkillsRequirmentResponse = await this.getJobSkills({
      jobDescription: job.data.application.job.requirement,
    });
    const jobSkillsResponsibilityResponse = await this.getJobSkills({
      jobDescription: job.data.application.job.responsibility,
    });
    jobSkillsResponse.push(...jobSkillsDescriptionResponse.data);
    jobSkillsResponse.push(...jobSkillsRequirmentResponse.data);
    jobSkillsResponse.push(...jobSkillsResponsibilityResponse.data);

    console.log('Job skills response:', jobSkillsResponse);

    if (jobSkillsResponse.length === 0) {
      throw new JobSkillNotFoundException();
    }

    // check QA/QC job skills
    QAQCJobSkillsResponseFilter.push(...this.QAQCJobSkills(jobSkillsResponse));
    console.log('Final job skills:', QAQCJobSkillsResponseFilter);
    // Create list of technique questions
    const technicalQuestions = await this.getTechniqueQuestions({
      jobExperienceLevel:
        jobExperienceLevel.charAt(0).toUpperCase() +
        jobExperienceLevel.slice(1),
      jobSkills: QAQCJobSkillsResponseFilter,
    });
    console.log('Technique questions:', technicalQuestions);

    // Create english questions
    const englishQuestions = ENGLISH_QUESTION;
    console.log('English questions:', englishQuestions);
    await this.prismaService.$transaction(
      async (prisma) => {
        // Create foudation test
        const foundationTest = await prisma.foundationTest.create({
          data: {
            applicationId: job.data.application.applicationId,
            status: TestStatus.TEST_PENDING,
          },
        });

        // Create technical test
        const technicalTest = await prisma.technicalTest.create({
          data: {
            foundationTest: {
              connect: {
                foundationTestId: foundationTest.foundationTestId,
              },
            },
            status: TestStatus.TEST_PENDING,
            score: 0,
          },
        });

        // Create technical test questions
        await prisma.technicalTestQuestion.createMany({
          data: technicalQuestions.map((question) => ({
            interviewQuestionId: question.interviewQuestionId,
            technicalTestId: technicalTest.technicalTestId,
            answer: '',
            score: 0,
          })),
        });

        // Create english test
        await prisma.englishTest.create({
          data: {
            foundationTest: {
              connect: {
                foundationTestId: foundationTest.foundationTestId,
              },
            },
            question: '',
            status: TestStatus.TEST_PENDING,
            userAnswer: '',
            score: 0,
          },
        });

        // Create conversation
        const { userId, firstName, lastName, cv } =
          job.data.application.userRole.profile;
        const { title, description, jobSkillLevel } = job.data.application.job;

        const conversation = await axios.post<CreateConversationResponse>(
          this.CREATE_CONVERSATION_URL,
          {
            user_id: userId,
            user_name: `${firstName} ${lastName}`,
            conversation_name: `${firstName} ${lastName}: Start tech interview for ${title}`,
            candidate_cv: cv[cv.length - 1],
            position: title,
            level: jobSkillLevel[0].skillLevel.name,
            job_description: description,
          },
        );

        console.log('Conversation:', conversation.data);

        await prisma.aIBotTest.create({
          data: {
            foundationTest: {
              connect: {
                foundationTestId: foundationTest.foundationTestId,
              },
            },
            status: TestStatus.TEST_PENDING,
            conversationId: conversation.data.data.conversation_id,
            score: 0,
          },
        });

        // Create live coding test
        const codingChallengeQuestion = await this.getCodingChallengeQuestions({
          jobExperienceLevel:
            jobExperienceLevel.charAt(0).toUpperCase() +
            jobExperienceLevel.slice(1),
        });
        console.log('Coding challenge question:', codingChallengeQuestion);
        const codingTest = await prisma.codingTest.create({
          data: {
            foundationTest: {
              connect: {
                foundationTestId: foundationTest.foundationTestId,
              },
            },
            status: TestStatus.TEST_PENDING,
            score: 0,
          },
        });
        await prisma.codingQuestion.create({
          data: {
            codingTestId: codingTest.codingTestId,
            interviewQuestionId: codingChallengeQuestion.interviewQuestionId,
            answer: '',
            score: 0,
          },
        });
      },
      {
        // transaction options
        timeout: 40000,
      },
    );
  }

  @Process({ name: TestTask.APPLICATION_TEST_V2_TASK, concurrency: 1 })
  async processApplicationTestV2(job: Job<IApplicationTest>): Promise<void> {
    // log data object to string
    console.log('Start processing job...', JSON.stringify(job.data));

    const { userId, firstName, lastName, cv } =
      job.data.application.userRole.profile;

    const { title, description, jobSkillLevel } = job.data.application.job;

    console.log('userId:', userId);
    console.log('firstName:', firstName);
    console.log('lastName:', lastName);
    console.log('cv:', cv[cv.length - 1]);
    console.log('title:', title);
    console.log('description:', description);
    console.log('jobSkillLevel:', jobSkillLevel);

    // Create conversation
    const conversation = await axios.post<CreateConversationResponse>(
      this.CREATE_CONVERSATION_URL,
      {
        user_id: userId,
        user_name: `${firstName} ${lastName}`,
        conversation_name: `${firstName} ${lastName}: Start tech interview for ${title}`,
        candidate_cv: cv[cv.length - 1],
        position: title,
        level: jobSkillLevel[0].skillLevel.name,
        job_description: description,
      },
    );

    console.log('Conversation:', conversation.data);

    await this.prismaService.technicalTest.create({
      data: {
        foundationTest: {
          connect: {
            foundationTestId:
              job.data.application.foundationTest.foundationTestId,
          },
        },
        status: TestStatus.TEST_PENDING,
        score: 0,
      },
    });
  }

  @OnQueueCompleted()
  completed(job: Job) {
    console.log(`Job ${job.id} has completed!`);
  }
}
