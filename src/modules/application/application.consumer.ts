import { Processor, Process, OnQueueCompleted } from '@nestjs/bull';
import { Job } from 'bull';
import { Application } from '@prisma/client';

import { QueueName, ApplicationTask } from './../../utils/enums';
import { ITaskApplicationConsumer } from './../../utils/interfaces/application.interface';

@Processor(QueueName.APPLICATION_QUEUE)
export class TaskApplicationConsumer implements ITaskApplicationConsumer {
  @Process(ApplicationTask.PENDING_APPLICATION_TASK)
  async processPendingApplication(job: Job<Application>): Promise<void> {
    console.log('Start processing job...', job.data);
  }

  @OnQueueCompleted()
  completed(job: Job) {
    console.log(`Job ${job.id} has completed!`);
  }
}
