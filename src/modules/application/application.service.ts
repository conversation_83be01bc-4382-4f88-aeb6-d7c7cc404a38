import { Injectable } from '@nestjs/common';
import { InjectQueue } from '@nestjs/bull';
import { Queue } from 'bull';
import { Application } from '@prisma/client';

import { QueueName, ApplicationTask, ApplicationStatus } from 'src/utils/enums';
import { PrismaService } from '../prisma/prisma.service';
import { IApplicationService } from 'src/utils/interfaces/application.interface';

@Injectable()
export class ApplicationService implements IApplicationService {
  constructor(
    @InjectQueue(QueueName.APPLICATION_QUEUE)
    private checkPendingApplicationQueue: Queue<Application>,
    private prismaService: PrismaService,
  ) {}

  async pendingApplication(): Promise<void> {
    const application = await this.prismaService.application.findFirst({
      where: {
        status: ApplicationStatus.APPLICATION_PENDING,
        foundationTest: {
          is: null,
        },
      },
      orderBy: {
        createdAt: 'asc',
      },
    });

    this.checkPendingApplicationQueue.add(
      ApplicationTask.PENDING_APPLICATION_TASK,
      application,
    );
  }
}
