import { Modu<PERSON> } from '@nestjs/common';
import { BullModule } from '@nestjs/bull';

import { TaskApplicationConsumer } from './application.consumer';
import { ApplicationService } from './application.service';
import { ApplicationController } from './application.controller';
import { QueueName } from './../../utils/enums';
import { PrismaModule } from '../prisma/prisma.module';

@Module({
  imports: [
    PrismaModule,
    BullModule.registerQueue({
      name: QueueName.APPLICATION_QUEUE,
    }),
  ],
  controllers: [ApplicationController],
  providers: [TaskApplicationConsumer, ApplicationService],
  exports: [],
})
export class ApplicationModule {}
