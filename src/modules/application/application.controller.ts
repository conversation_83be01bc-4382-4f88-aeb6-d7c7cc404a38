import { <PERSON>, <PERSON> } from '@nestjs/common';

import { PrismaService } from '../prisma/prisma.service';

@Controller('application')
export class ApplicationController {
  constructor(private readonly Prisma: PrismaService) {}

  // @Post()
  // async createApplication() {
  //   return await this.Prisma.application.create({
  //     data: {
  //       status: 'PENDING_APPLICATION',
  //       userRole: {
  //         connect: {
  //           userId_roleId: {
  //             userId: 1,
  //             roleId: 1,
  //           },
  //         },
  //       },
  //     },
  //   });
  // }
}
