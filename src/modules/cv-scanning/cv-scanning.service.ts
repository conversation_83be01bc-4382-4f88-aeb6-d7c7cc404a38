import { Injectable } from '@nestjs/common';
import { InjectQueue } from '@nestjs/bull';
import { Queue } from 'bull';
import { Interval } from '@nestjs/schedule';
import { TaskStatus } from '@prisma/client';

import {
  ICvScanningService,
  ICvScanning,
} from 'src/utils/interfaces/cv-scanning.interface';
import { QueueName, CvScanningTask } from 'src/utils/enums';
import { PrismaService } from '../prisma/prisma.service';

@Injectable()
export class CvScanningService implements ICvScanningService {
  constructor(
    @InjectQueue(QueueName.CV_SCANNING_QUEUE)
    private cvScanningQueue: Queue<ICvScanning>,
    private prismaService: PrismaService,
  ) {}

  // @Interval(1 * 60 * 1000)
  // async cvScanning(): Promise<void> {
  //   const CVs = await this.prismaService.cVTask.findMany({
  //     where: { status: TaskStatus.SCHEDULED },
  //     orderBy: { createdAt: 'asc' },
  //     include: {
  //       candidates: {
  //         include: {
  //           Profile: { include: { academy: true, employment: true } },
  //         },
  //       },
  //       job: true,
  //     },
  //   });
  //   for (const cv of CVs) {
  //     this.cvScanningQueue.add(
  //       CvScanningTask.CV_SCANNING_TASK,
  //       { cVTask: cv },
  //       {
  //         jobId: `cvScanning-${cv.taskId}`,
  //       },
  //     );
  //   }
  // }

  // @Interval(10 * 1000)
  // async cancelCvScanning(): Promise<void> {
  //   const cancelledCVs = await this.prismaService.cVTask.findMany({
  //     where: { status: TaskStatus.CANCELLED },
  //   });

  //   for (const cv of cancelledCVs) {
  //     const job: Job | null = await this.cvScanningQueue.getJob(cv.taskId);
  //     if (job && (await job.isActive())) {
  //       await job.discard();
  //       await job.remove();
  //       console.log(`Cv task ${cv.taskId} has been stopped.`);
  //     }
  //   }
  // }
}
