import { Modu<PERSON> } from '@nestjs/common';
import { BullModule } from '@nestjs/bull';
import { PrismaModule } from '../prisma/prisma.module';

import { CvScanningService } from './cv-scanning.service';
import { CvScanningController } from './cv-scanning.controller';
import { TaskScanningCvConsumer } from './cv-scanning.consumer';
import { QueueName } from 'src/utils/enums';

@Module({
  imports: [
    BullModule.registerQueue({
      name: QueueName.CV_SCANNING_QUEUE,
    }),
    PrismaModule,
  ],
  controllers: [CvScanningController],
  providers: [CvScanningService, TaskScanningCvConsumer],
  exports: [CvScanningService],
})
export class CvScanningModule {}
