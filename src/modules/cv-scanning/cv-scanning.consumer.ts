import { Processor, Process, OnQueueCompleted } from '@nestjs/bull';
import { Job } from 'bull';
import * as fs from 'fs';
import axios from 'axios';
import * as path from 'path';
import * as FormData from 'form-data';
import { TaskStatus, CVTaskCandidate } from '@prisma/client';
import redisClient from 'src/configs/redis.provider';

import { QueueName, CvScanningTask } from 'src/utils/enums';
import {
  ICvScanning,
  ICvScanningConsumer,
  ICVTaskCandidate,
} from 'src/utils/interfaces/cv-scanning.interface';
import { PrismaService } from '../prisma/prisma.service';
import {
  ScanningCvResponse,
  CvScoreRequest,
  ScanningCvAcademicHistoriesResponse,
  ScanningCvWorkingPositionHistoriesResponse,
  ScanningCvKeywordResponse,
  ScanningJdResponse,
  ScanningCvScoreResponse,
  CandidateDetailResponse,
} from 'src/utils/types/cv-scanning.type';
import { BadRequestException } from 'src/exceptions';

@Processor(QueueName.CV_SCANNING_QUEUE)
export class TaskScanningCvConsumer implements ICvScanningConsumer {
  private readonly FILE_DIRECTORY = 'storage/scanning-cv';
  private readonly FILE_PATH = path.join(this.FILE_DIRECTORY, 'cv.pdf');
  private readonly CV_SCAN_URL = process.env.CV_SCAN_URL;
  private readonly ML_TOKEN = process.env.ML_TOKEN;

  private readonly CV_SCANNING_URL = `${this.CV_SCAN_URL}/api/hr-internal/upload-cv`;
  private readonly CV_ACADEMIC_SCANNING_URL = `${this.CV_SCAN_URL}/api/hr-internal/scan-cv-academic-histories`;
  private readonly CV_WORKING_POSITION_SCANNING_URL = `${this.CV_SCAN_URL}/api/hr-internal/scan-cv-working-position-histories`;
  private readonly CV_KEYWORD_SCANNING_URL = `${this.CV_SCAN_URL}/api/hr-internal/scan-cv-extract-keyword`;
  private readonly JD_SCANNING_URL = `${this.CV_SCAN_URL}/api/hr-internal/scan-jd`;
  private readonly CV_SCANNING_SCORE_URL = `${this.CV_SCAN_URL}/api/hr-internal/score-cv-jd`;

  constructor(private readonly prismaService: PrismaService) {}

  async downloadFileFromS3(s3Url: string): Promise<boolean> {
    try {
      if (!fs.existsSync(this.FILE_DIRECTORY)) {
        fs.mkdirSync(this.FILE_DIRECTORY, { recursive: true });
      }

      const writer = fs.createWriteStream(this.FILE_PATH);
      const response = await axios({
        url: s3Url,
        method: 'GET',
        responseType: 'stream',
      });

      await new Promise((resolve, reject) => {
        response.data.pipe(writer);
        writer.on('finish', () => {
          console.log('File downloaded successfully');
          resolve(true);
        });
        writer.on('error', (error) => {
          reject(error);
          console.log('Error downloading file:', error);
          throw new BadRequestException(`Error downloading file`);
        });
      });
      return true;
    } catch (error) {
      console.log('Error downloading file:', error);
      return false;
      // throw new BadRequestException(`Error downloading file`);
    }
  }

  async uploadFileToML(): Promise<ScanningCvResponse> {
    try {
      const formData = new FormData();
      formData.append('file', fs.createReadStream(this.FILE_PATH));

      const response = await axios.post<ScanningCvResponse>(
        this.CV_SCANNING_URL,
        formData,
        {
          headers: {
            ...formData.getHeaders(),
            Authorization: `Bearer ${this.ML_TOKEN}`,
          },
        },
      );
      console.log('Scanning CV successfully');
      return response.data;
    } catch (error) {
      console.log('Error scanning CV:', error);
      throw new BadRequestException(`Error scanning CV`);
    }
  }

  async postToApi<T>(url: string, data: any): Promise<T> {
    try {
      const response = await axios.post<T>(url, data, {
        headers: { Authorization: `Bearer ${this.ML_TOKEN}` },
      });

      console.log('Processing candidate detail successfully');
      return response.data;
    } catch (error) {
      console.log('Error', error);
      throw new BadRequestException();
    }
  }

  async processCandidateDetail(
    s3_url: string,
    candidate: ICVTaskCandidate,
    job: Job<ICvScanning>,
  ) {
    const academicHistories =
      await this.postToApi<ScanningCvAcademicHistoriesResponse>(
        this.CV_ACADEMIC_SCANNING_URL,
        { cv_url: s3_url },
      );
    // store cost infor
    await this.saveTokenUsage(
      job,
      candidate,
      academicHistories.meta_data,
      'academicHistories',
    );

    const workingPositionHistories =
      await this.postToApi<ScanningCvWorkingPositionHistoriesResponse>(
        this.CV_WORKING_POSITION_SCANNING_URL,
        { cv_url: s3_url },
      );

    // store cost infor
    await this.saveTokenUsage(
      job,
      candidate,
      workingPositionHistories.meta_data,
      'workingPositionHistories',
    );
    const skills = await this.postToApi<ScanningCvKeywordResponse>(
      this.CV_KEYWORD_SCANNING_URL,
      { cv_url: s3_url },
    );
    await this.saveTokenUsage(job, candidate, skills.meta_data, 'skills');

    await this.prismaService.cVTaskCandidate.update({
      where: {
        taskId_profileId: {
          taskId: candidate.taskId,
          profileId: candidate.profileId,
        },
      },
      data: {
        candidateDetailScanning: {
          academicHistories: academicHistories.data,
          workingPositionHistories: workingPositionHistories.data,
          skills: skills.data,
        },
      },
    });
    const data: CandidateDetailResponse = {
      academicHistories: academicHistories,
      workingPositionHistories: workingPositionHistories,
      skills: skills,
    };
    return data;
  }

  async processJdDetail(jobDescription: string, job: Job<ICvScanning>, candidate: ICVTaskCandidate) {
    try {
      const jdDetailResponse = await this.postToApi<ScanningJdResponse>(
        this.JD_SCANNING_URL,
        { jd: jobDescription },
      );

      await this.prismaService.cVTask.update({
        where: { taskId: job.data.cVTask.taskId },
        data: { jobDetailScanning: jdDetailResponse.data },
      });

      // store cost infor
      await this.saveTokenUsage(
        job,
        candidate,
        jdDetailResponse.meta_data,
        'jdDetail',
      );

      console.log('Processing JD detail successfully');
      return jdDetailResponse;
    } catch (error) {
      console.log('Error processing JD detail:', error);
      throw new BadRequestException(`Error processing JD detail`);
    }
  }

  async calculateCvScore(
    scanningCvResponse: ScanningCvResponse,
    candidateDetailData: {
      academicHistories: ScanningCvAcademicHistoriesResponse;
      workingPositionHistories: ScanningCvWorkingPositionHistoriesResponse;
      skills: ScanningCvKeywordResponse;
    },
    jdDetail: ScanningJdResponse,
    job: Job<ICvScanning>,
    candidate: ICVTaskCandidate,
  ) {
    const cvScoreRequest: CvScoreRequest = {
      jd: {
        title: jdDetail.data.title ? jdDetail.data.title : 'Not specified',
        description: jdDetail.data.description
          ? jdDetail.data.description
          : 'Not specified',
        location: jdDetail.data.location ? jdDetail.data.location : 'Remote',
        starting_salary: jdDetail.data.starting_salary
          ? jdDetail.data.starting_salary
          : '-1',
        ending_salary: jdDetail.data.ending_salary
          ? jdDetail.data.ending_salary
          : '-1',
        currency_salary: jdDetail.data.currency_salary
          ? jdDetail.data.currency_salary
          : '-1',
        posted_date: new Date(job.data.cVTask.job.postedDate)
          .toISOString()
          .slice(0, 10),
        benefit: jdDetail.data.benefit
          ? jdDetail.data.benefit
          : 'Not specified',
        requirement: jdDetail.data.requirement
          ? jdDetail.data.requirement
          : 'Not specified',
        responsibility: jdDetail.data.responsibility
          ? jdDetail.data.responsibility
          : 'Not specified',
      },
      candidate: {
        name:
          `${scanningCvResponse.data.first_name} ${scanningCvResponse.data.last_name}` ||
          'Not specified',
        email: scanningCvResponse.data.email
          ? scanningCvResponse.data.email
          : 'Not specified',
        phone: scanningCvResponse.data.phone
          ? scanningCvResponse.data.phone
          : 'Not specified',
        university: scanningCvResponse.data.university
          ? scanningCvResponse.data.university
          : 'Not specified',
        language_skill: scanningCvResponse.data.language_skill
          ? scanningCvResponse.data.language_skill
          : 'Not specified',
        address: scanningCvResponse.data.address
          ? scanningCvResponse.data.address
          : 'Not specified',
        birthday: scanningCvResponse.data.birth_date
          ? scanningCvResponse.data.birth_date
          : '1970-01-01',
        current_salary: '-1',
        ideal_salary: '-1',
        minimum_salary: '-1',
        skills:
          candidateDetailData.skills?.data
            .map((skill) => skill.skillName)
            .join(',') || 'Not specified',
        working_position_histories:
          candidateDetailData.workingPositionHistories?.data || [],
        academic_histories: candidateDetailData.academicHistories?.data || [],
      },
    };
    console.log('cvScoreRequest:', cvScoreRequest);
    let retries = 3;
    while (retries > 0) {
      try {
        const cvScoreResponse = await this.postToApi<ScanningCvScoreResponse>(
          this.CV_SCANNING_SCORE_URL,
          cvScoreRequest,
        );
        console.log('Calculating CV score successfully');
        console.log('cvScoreResponse:', cvScoreResponse);
        return cvScoreResponse;
        // store cost infor
        await this.saveTokenUsage(
          job,
          candidate,
          cvScoreResponse.meta_data,
          'jdDetail',
        );
      } catch (error) {
        // timeout 20s
        await new Promise((resolve) => setTimeout(resolve, 20000));
        retries--;
      }
    }

    throw new BadRequestException('Error calculating CV score');
  }

  async saveTokenUsage(
    job: Job<ICvScanning>,
    candidate: ICVTaskCandidate,
    metaData: any,
    type: string,
  ): Promise<void> {
    if (!metaData) {
      console.warn('⚠️ metaData is missing, skipping log.');
      return;
    }

    try {
      await this.prismaService.tokenUsageLog.create({
        data: {
          taskId: job.data.cVTask.taskId,
          jobId: job.data.cVTask.jobId,
          profileId: candidate.Profile.profileId,
          inputTokens: metaData.input_tokens,
          outputTokens: metaData.output_tokens,
          requestType: type,
          inputTokenDetail: metaData.input_token_details,
          outputTokenDetail: metaData.output_token_details,
        },
      });

      console.log('✅ Token usage log has been recorded.');
    } catch (error) {
      console.error('❌ Failed to log token usage:', error);
    }
  }

  async cacheCandidateData(
    profileId: number,
    jobId: number,
  ): Promise<CVTaskCandidate> {
    const sevenDaysAgo = new Date();
    sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7);

    const candidates = await this.prismaService.cVTaskCandidate.findMany({
      where: {
        profileId,
        createdAt: {
          gte: sevenDaysAgo,
        },
      },
      include: { CVTask: { include: { job: true } } },
    });

    for (const candidate of candidates) {
      if (candidate.CVTask.job.jobId === jobId) return candidate;
    }

    return null;
  }
  // async cacheCandidateData(
  //   profileId: number,
  //   jobId: number,
  // ): Promise<CVTaskCandidate> {
  //   const sevenDaysAgo = new Date();
  //   sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7);

  //   const candidates = await this.prismaService.cVTaskCandidate.findMany({
  //     where: {
  //       profileId,
  //       createdAt: {
  //         gte: sevenDaysAgo,
  //       },
  //     },
  //     include: { CVTask: { include: { job: true } } },
  //   });

  //   for (const candidate of candidates) {
  //     if (candidate.CVTask.job.jobId === jobId && candidate.score)
  //       return candidate;
  //   }

  //   return null;
  // }

  @Process({
    name: CvScanningTask.CV_SCANNING_TASK,
    concurrency: 1,
  })
  async processScanningCv(job: Job<ICvScanning>): Promise<void> {
    console.log('Start processing job...', job.data);
    const ONE_WEEK_IN_SECONDS = 7 * 24 * 60 * 60;

    // Processing
    await this.prismaService.cVTask.update({
      where: { taskId: job.data.cVTask.taskId },
      data: { status: TaskStatus.RUNNING },
    });

    for (const candidate of job.data.cVTask.candidates) {
      const jobDescriptionKey = `${job.data.cVTask.jobId}-${candidate.profileId}-jobDescription`;
      const candidateDetailKey = `${job.data.cVTask.jobId}-${candidate.profileId}-jobDetail`;
      const scoreKey = `${job.data.cVTask.jobId}-${candidate.profileId}-score`;

      console.log('Processing candidate...', candidate);
      if (candidate.score) continue;

      // // Cache candidate data
      // const candidateCached = await this.cacheCandidateData(
      //   candidate.profileId,
      //   job.data.cVTask.job.jobId,
      // );

      // if (candidateCached) {
      //   console.log('Candidate cached:', candidateCached);
      //   await this.prismaService.cVTaskCandidate.update({
      //     where: {
      //       taskId_profileId: {
      //         taskId: candidate.taskId,
      //         profileId: candidate.profileId,
      //       },
      //     },
      //     data: {
      //       score: candidateCached.score,
      //       candidateDetailScanning: candidateCached.candidateDetailScanning,
      //     },
      //   });
      //   continue;
      // }

      const s3Url = candidate.Profile.cv[candidate.Profile.cv.length - 1];

      // Download candidate cv from S3
      const isDownloaded = await this.downloadFileFromS3(s3Url);

      // Scanning dadidate cv
      const candidateScanningCvResponse = await this.uploadFileToML();
      const { s3_url } = candidateScanningCvResponse;

      // Process candidate detail
      let candidateDetailData: CandidateDetailResponse;

      const cachedCandidateDetailValue =
        await redisClient.get(candidateDetailKey);
      if (cachedCandidateDetailValue) {
        console.log('CandidateDetail is existing, get from redis...');
        candidateDetailData = JSON.parse(
          cachedCandidateDetailValue,
        ) as CandidateDetailResponse;
      } else {
        candidateDetailData = await this.processCandidateDetail(
          s3_url,
          candidate,
          job
        );
        console.log('Call API to get CandidateDetail...');
        await redisClient.set(
          candidateDetailKey,
          JSON.stringify(candidateDetailData),
          'EX',
          ONE_WEEK_IN_SECONDS,
        );
      }

      // Process job description
      const jobDescription = `${job.data.cVTask.job.description} ${job.data.cVTask.job.requirement} ${job.data.cVTask.job.responsibility}`;
      let jdDetailResponse: ScanningJdResponse;

      const cachedJobDescriptionValue =
        await redisClient.get(jobDescriptionKey);
      if (cachedJobDescriptionValue) {
        console.log('JobDetail is existing, get from redis...');
        jdDetailResponse = JSON.parse(
          cachedJobDescriptionValue,
        ) as ScanningJdResponse;
      } else {
        jdDetailResponse = await this.processJdDetail(jobDescription, job, candidate);
        console.log('Call API to get JobDetail...');
        await redisClient.set(
          jobDescriptionKey,
          JSON.stringify(jdDetailResponse),
          'EX',
          ONE_WEEK_IN_SECONDS,
        );
      }

      if (
        !jdDetailResponse ||
        !candidateDetailData.skills ||
        !candidateDetailData.academicHistories ||
        !candidateDetailData.workingPositionHistories
      )
        throw new BadRequestException('Missing data to calculate CV score');

      // Calculate CV score
      let scoreRes: ScanningCvScoreResponse;

      const scoreValue = await redisClient.get(scoreKey);
      if (scoreValue) {
        console.log('Score is existing, get from redis...');
        scoreRes = JSON.parse(scoreValue) as ScanningCvScoreResponse;
      } else if (!isDownloaded) {
        scoreRes = {
          score: 0,
        };
      } else {
        scoreRes = await this.calculateCvScore(
          candidateScanningCvResponse,
          candidateDetailData,
          jdDetailResponse,
          job,
          candidate,
        );
        console.log('Call API to get Score...');
        await redisClient.set(
          scoreKey,
          JSON.stringify(scoreRes),
          'EX',
          ONE_WEEK_IN_SECONDS,
        );
      }
      console.log('cvScoreRes:', scoreRes);

      // Update candidate score
      await this.prismaService.cVTaskCandidate.update({
        where: {
          taskId_profileId: {
            taskId: candidate.taskId,
            profileId: candidate.profileId,
          },
        },
        data: { score: scoreRes.score },
      });
    }

    // Completed
    await this.prismaService.cVTask.update({
      where: { taskId: job.data.cVTask.taskId },
      data: { status: TaskStatus.COMPLETE },
    });
  }

  // @Process({
  //   name: CvScanningTask.CV_SCANNING_CANCEL_TASK,
  //   concurrency: 1,
  // })
  // async processCancelScanningCv(job: Job<ICvScanning>): Promise<void> {
  //   console.log('Start canceling job...', job.data);
  // }

  @OnQueueCompleted()
  completed(job: Job) {
    console.log(`Job ${job.id} has completed!`);
  }
}
