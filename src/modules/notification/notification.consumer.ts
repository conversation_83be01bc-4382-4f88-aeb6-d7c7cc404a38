import { Processor, Process, OnQueueCompleted } from '@nestjs/bull';
import { Job } from 'bull';
import { OAuth2Client } from 'google-auth-library';
import { google } from 'googleapis';
import * as fs from 'fs';
import { MeetingStatus, ContactSupportStatus } from '@prisma/client';

import {
  QueueName,
  NotificationTask,
  TestStatus,
  EmailStructure,
  Language,
  EmailType,
} from 'src/utils/enums';
import {
  ITaskNotificationConsumer,
  INotification,
} from 'src/utils/interfaces/notification.interface';
import { MailService } from '../mail/mail.service';
import { AwsSesService } from '../aws/aws.ses.service';
import { PrismaService } from '../prisma/prisma.service';
import {
  getTextByLanguageCode,
  getEmailBookingParams,
  getScanCvEmailParams,
  getFoundationTestParams,
} from 'src/shared/helpers/mail.helpers';
import { SlackService } from '../slack/slack.service';
import {
  GoogleCredentials,
  GoogleToken,
} from 'src/utils/types/booking-demo.type';
import {
  BOOKING_DEMO_SUMMARY,
  meetingDescription,
} from 'src/utils/constants/booking-demo.constants';
import { getEnvironmentLandingLink } from 'src/utils/constants/global.constants';

@Processor(QueueName.NOTIFICATION_QUEUE)
export class TaskNotificationConsumer implements ITaskNotificationConsumer {
  constructor(
    private readonly mailService: MailService,
    private readonly awsSesService: AwsSesService,
    private readonly prismaService: PrismaService,
    private readonly slackService: SlackService,
  ) {}

  private oAuth2Client: OAuth2Client;
  private readonly TOKEN_PATH = 'token.json';
  private readonly CREDENTIALS_PATH = 'credentials.json';
  private readonly EMAIL_HOST_ID = process.env.EMAIL_HOST_ID;
  private readonly CV_SCANNING_EMAIL_TO = process.env.CV_SCANNING_EMAIL_TO;
  private readonly CONTACT_SUPPORT_EMAIL_TO =
    process.env.CONTACT_SUPPORT_EMAIL_TO;

  @Process({
    name: NotificationTask.PENDING_FOUNDATION_TEST_NOTIFICATION_TASK,
    concurrency: 1,
  })
  async processPendingFoundationTestNotification(
    job: Job<INotification>,
  ): Promise<void> {
    console.log('Start processing job...', job.data);
    const user = job.data.foundationTest.application.userRole.user;
    const to = user.email || '<EMAIL>';

    const params = getFoundationTestParams(job.data);

    const res = await this.mailService.sendDynamicEmail({
      to: to,
      templateId: process.env.PENDING_FOUNDATION_TEST_TEMPLATE_ID,
      params,
    });
    // const res = await this.awsSesService.sendEmail({ to, subject, text, html });
    console.log('mail response', { res });

    await this.prismaService.foundationTest.update({
      where: { foundationTestId: job.data.foundationTest.foundationTestId },
      data: { status: TestStatus.TEST_NOTIFIED },
    });
  }

  @Process({
    name: NotificationTask.PENDING_TEST_RESULT_NOTIFICATION_TASK,
    concurrency: 1,
  })
  async processPendingTestResultNotification(
    job: Job<INotification>,
  ): Promise<void> {
    // send notification
    console.log('Start processing job...', job.data);

    const user = job.data.foundationTest.application.userRole.user;
    const to = user.email || '<EMAIL>';

    const params = getFoundationTestParams(job.data);

    const res = await this.mailService.sendDynamicEmail({
      to: to,
      templateId: process.env.PENDING_TEST_RESULT_TEMPLATE_ID,
      params,
    });
    // const res = await this.awsSesService.sendEmail({ to, subject, text, html });
    console.log('mail response', { res });

    await this.prismaService.foundationTest.update({
      where: { foundationTestId: job.data.foundationTest.foundationTestId },
      data: { status: TestStatus.TEST_NOTIFIED },
    });

    // Send slack notification for internal team
    this.slackService.sendTestResults(job.data);
  }

  @Process({
    name: NotificationTask.SCHEDULE_MEETING_NOTIFICATION_TASK,
    concurrency: 3,
  })
  async procesScheduleMeetingNotification(
    job: Job<INotification>,
  ): Promise<void> {
    // read credentials file
    const credentials = fs.readFileSync(this.CREDENTIALS_PATH);
    const parsedCredentials: GoogleCredentials = JSON.parse(
      credentials.toString('utf8'),
    );
    const token = fs.readFileSync(this.TOKEN_PATH);
    const parsedToken: GoogleToken = JSON.parse(token.toString('utf8'));
    const { client_id, client_secret, redirect_uris } =
      parsedCredentials.installed;
    try {
      this.oAuth2Client = new google.auth.OAuth2(
        client_id,
        client_secret,
        redirect_uris[0],
      );
      this.oAuth2Client.setCredentials(parsedToken);
      const tokens = await this.oAuth2Client.refreshAccessToken();
      fs.writeFileSync(this.TOKEN_PATH, JSON.stringify(tokens.res.data));

      const calendar = google.calendar({
        version: 'v3',
        auth: this.oAuth2Client,
      });
      const startTime = new Date(job.data.meeting.startTime).toISOString();
      const endTime = new Date(job.data.meeting.endTime).toISOString();
      const summary = job.data.meeting
        .MeetingUser_Meeting_customerIdToMeetingUser
        ? BOOKING_DEMO_SUMMARY
        : 'Buffer';
      const attendees = job.data.meeting
        .MeetingUser_Meeting_customerIdToMeetingUser
        ? [
            {
              email:
                job.data.meeting.MeetingUser_Meeting_customerIdToMeetingUser
                  .workEmail,
            },
            {
              email:
                job.data.meeting.MeetingUser_Meeting_hostIdToMeetingUser
                  .workEmail,
            },
          ]
        : [
            {
              email:
                job.data.meeting.MeetingUser_Meeting_hostIdToMeetingUser
                  .workEmail,
            },
          ];
      const eventDetails = {
        summary,
        description: meetingDescription(
          `${getEnvironmentLandingLink()}/request-demo/reschedule?rescheduleId=${job.data.meeting.meetingId}`,
          `${getEnvironmentLandingLink()}/request-demo/cancel/${job.data.meeting.meetingId}`,
        ),
        start: {
          dateTime: startTime,
        },
        end: {
          dateTime: endTime,
        },
        attendees,
        conferenceData: {
          createRequest: {
            requestId: `demo-meeting-${Date.now()}`, // Unique request ID
            conferenceSolutionKey: { type: 'hangoutsMeet' },
          },
        },
      };

      // Create event in Google Calendar
      const event = await calendar.events.insert({
        calendarId: this.EMAIL_HOST_ID,
        requestBody: eventDetails,
        conferenceDataVersion: 1,
      });

      // Check if busyTime record already exists
      const existingBusyTime = await this.prismaService.busyTime.findFirst({
        where: {
          meetingUserId:
            job.data.meeting.MeetingUser_Meeting_hostIdToMeetingUser
              .meetingUserId,
          startTime: startTime,
          endTime: endTime,
          isActive: true,
        },
      });

      if (!existingBusyTime) console.log('can not find existing busy time');

      /*if (!existingBusyTime) {
        await this.prismaService.busyTime.create({
          data: {
            startTime,
            endTime,
            meetingUserId:
              job.data.meeting.MeetingUser_Meeting_hostIdToMeetingUser
                .meetingUserId,
          },
        });
      }*/

      console.log('Event created: %s', event.data);

      await this.prismaService.meeting.update({
        where: {
          meetingId: job.data.meeting.meetingId,
        },
        data: {
          status: MeetingStatus.SUCCESSFULLY_SCHEDULED,
          linkMeeting: event.data.hangoutLink,
          calendarId: this.EMAIL_HOST_ID,
          calendarEventId: event.data.id,
        },
      });

      // no customer, it mean that this event for buffer time
      if (!job.data.meeting.MeetingUser_Meeting_customerIdToMeetingUser) return;

      // send notification
      job.data.meeting.meetingLink = event.data.hangoutLink;
      const toCustomer =
        job.data.meeting.MeetingUser_Meeting_customerIdToMeetingUser.workEmail;
      const toHost =
        job.data.meeting.MeetingUser_Meeting_hostIdToMeetingUser.workEmail;

      const params = getEmailBookingParams(job.data);

      const mailForCustomer = await this.mailService.sendDynamicEmail({
        to: toCustomer,
        templateId: process.env.SCHEDULE_MEETING_TEMPLATE_ID,
        params: params,
      });
      const mailForHost = await this.mailService.sendDynamicEmail({
        to: toHost,
        templateId: process.env.SCHEDULE_MEETING_TEMPLATE_ID,
        params: params,
      });
      // const res = await this.awsSesService.sendEmail({ to, subject, text, html });
      console.log('done book meeting');
      console.log('mail response', { mailForCustomer });
      console.log('mail response', { mailForHost });
    } catch (error) {
      console.log(error, 'error');
    }
  }

  @Process({
    name: NotificationTask.CANCEL_MEETING_NOTIFICATION_TASK,
    concurrency: 3,
  })
  async processCancelMeetingNotification(
    job: Job<INotification>,
  ): Promise<void> {
    const { meeting } = job.data;
    // read credentials file
    const credentials = fs.readFileSync(this.CREDENTIALS_PATH);
    const parsedCredentials: GoogleCredentials = JSON.parse(
      credentials.toString('utf8'),
    );
    const token = fs.readFileSync(this.TOKEN_PATH);
    const parsedToken: GoogleToken = JSON.parse(token.toString('utf8'));
    const { client_id, client_secret, redirect_uris } =
      parsedCredentials.installed;
    try {
      this.oAuth2Client = new google.auth.OAuth2(
        client_id,
        client_secret,
        redirect_uris[0],
      );
      this.oAuth2Client.setCredentials(parsedToken);
      const tokens = await this.oAuth2Client.refreshAccessToken();
      fs.writeFileSync(this.TOKEN_PATH, JSON.stringify(tokens.res.data));

      const calendar = google.calendar({
        version: 'v3',
        auth: this.oAuth2Client,
      });

      await calendar.events.delete({
        calendarId: this.EMAIL_HOST_ID,
        eventId: meeting.calendarEventId,
      });

      const busyTime = await this.prismaService.busyTime.findMany({
        where: {
          meetingUserId: meeting.hostId,
          startTime: meeting.startTime,
          endTime: meeting.endTime,
          isActive: true,
        },
      });

      console.log('busyTime', busyTime);

      if (busyTime.length > 0) {
        console.log('update busy time');
        await this.prismaService.busyTime.updateMany({
          where: {
            busyTimeId: { in: busyTime.map((busy) => busy.busyTimeId) },
            isActive: true,
          },
          data: { isActive: false },
        });
      }

      // no customer, it mean that this event for buffer time
      if (!job.data.meeting.MeetingUser_Meeting_customerIdToMeetingUser) return;

      // send notification
      const toCustomer =
        job.data.meeting.MeetingUser_Meeting_customerIdToMeetingUser.workEmail;

      const params = getEmailBookingParams(job.data);

      const mailForCustomer = await this.mailService.sendDynamicEmail({
        to: toCustomer,
        templateId: process.env.CANCEL_MEETING_TEMPLATE_ID,
        params,
      });

      console.log('mail response', { mailForCustomer });
    } catch (error) {
      console.log(error, 'error');
    }
  }

  @Process({
    name: NotificationTask.CV_SCANNING_NOTIFICATION_TASK,
    concurrency: 1,
  })
  async processCvScanningNotification(job: Job<INotification>): Promise<void> {
    console.log('Start processing job...', job.data);

    // send notification
    const params = getScanCvEmailParams(job.data);

    const res = await this.mailService.sendDynamicEmail({
      to: this.CV_SCANNING_EMAIL_TO,
      templateId: process.env.CV_SCANNING_TEMPLATE_ID,
      params: params,
    });

    console.log('mail response', { res });
  }

  @Process({
    name: NotificationTask.REMINDER_EMAIL_NOTIFICATION_TASK,
    concurrency: 3,
  })
  async proceReminderMeetingNotification(
    job: Job<INotification>,
  ): Promise<void> {
    console.log('Start processing email reminder job...', job.data);
    // send notification
    const toCustomer =
      job.data.meeting.MeetingUser_Meeting_customerIdToMeetingUser.workEmail;

    const params = getEmailBookingParams(job.data);
    await this.mailService.sendDynamicEmail({
      to: toCustomer,
      templateId: process.env.REMINDER_EMAIL_TEMPLATE_ID,
      params: params,
    });
  }

  @Process({
    name: NotificationTask.CONTACT_SUPPORT_NOTIFICATION_TASK,
    concurrency: 1,
  })
  async processContactSupportNotification(
    job: Job<INotification>,
  ): Promise<void> {
    console.log('Start processing job...', job.data);

    // send notification
    const language = Language.EN;
    const subject = getTextByLanguageCode(
      EmailType.CONTACT_SUPPORT,
      EmailStructure.EMAIL_SUBJECT,
      language,
      job.data,
    );
    const text = getTextByLanguageCode(
      EmailType.CONTACT_SUPPORT,
      EmailStructure.EMAIL_TEXT,
      language,
      job.data,
    );
    const html = getTextByLanguageCode(
      EmailType.CONTACT_SUPPORT,
      EmailStructure.EMAIL_HTML,
      language,
      job.data,
    );

    const res = await this.mailService.sendEmail({
      to: this.CONTACT_SUPPORT_EMAIL_TO,
      subject,
      text,
      html,
    });

    console.log('mail response', { res });

    await this.prismaService.contactSupport.update({
      where: { contactSupportId: job.data.contactSupport.contactSupportId },
      data: { status: ContactSupportStatus.NOTIFIED },
    });
  }

  @OnQueueCompleted()
  completed(job: Job) {
    console.log(`Job ${job.id} has completed!`);
  }
}
