import { Injectable } from '@nestjs/common';
import { InjectQueue } from '@nestjs/bull';
import { Queue } from 'bull';
import { Interval } from '@nestjs/schedule';
import {
  MeetingStatus,
  TaskStatus,
  ContactSupportStatus,
} from '@prisma/client';
import { <PERSON>ron } from '@nestjs/schedule';
import { addDays, startOfDay, endOfDay } from 'date-fns';

import {
  INotificationService,
  INotification,
  IFoundationTest,
} from 'src/utils/interfaces/notification.interface';
import { PrismaService } from '../prisma/prisma.service';
import { TestStatus } from 'src/utils/enums';
import { QueueName } from 'src/utils/enums';
import { NotificationTask } from 'src/utils/enums';

@Injectable()
export class NotificationService implements INotificationService {
  constructor(
    private readonly prismaService: PrismaService,
    @InjectQueue(QueueName.NOTIFICATION_QUEUE)
    private notificationQueue: Queue<INotification>,
  ) {}

  @Interval(10 * 1000)
  async scheduleMeetingNotification(): Promise<void> {
    const meetings = await this.prismaService.meeting.findMany({
      where: {
        status: MeetingStatus.SCHEDULING,
      },
      orderBy: { createdAt: 'asc' },
      include: {
        MeetingUser_Meeting_customerIdToMeetingUser: true,
        MeetingUser_Meeting_hostIdToMeetingUser: true,
      },
    });

    for (const meeting of meetings) {
      this.notificationQueue.add(
        NotificationTask.SCHEDULE_MEETING_NOTIFICATION_TASK,
        {
          meeting,
        },
        {
          jobId: `scheduleMeeting-${meeting.meetingId}`,
        },
      );
    }
  }

  @Interval(10 * 1000)
  async cancelMeetingNotification(): Promise<void> {
    const meetings = await this.prismaService.meeting.findMany({
      where: {
        status: MeetingStatus.CANCELLED,
      },
      include: {
        MeetingUser_Meeting_customerIdToMeetingUser: true,
        MeetingUser_Meeting_hostIdToMeetingUser: true,
      },
    });

    for (const meeting of meetings) {
      this.notificationQueue.add(
        NotificationTask.CANCEL_MEETING_NOTIFICATION_TASK,
        {
          meeting,
        },
        {
          jobId: `cancelmeeting-${meeting.meetingId}`,
          // removeOnComplete: true,
        },
      );
    }
  }

  @Interval(60 * 60 * 1000)
  async reminderMeetingNotification(): Promise<void> {
    const daysOffset = parseInt(process.env.REMIND_BEFORE_DAYS || '1', 10);

    const tomorrowStart = startOfDay(addDays(new Date(), daysOffset));
    const tomorrowEnd = endOfDay(addDays(new Date(), daysOffset));
    const meetings = await this.prismaService.meeting.findMany({
      where: {
        status: MeetingStatus.SUCCESSFULLY_SCHEDULED,
        startTime: {
          gte: tomorrowStart.toISOString().split('T')[0] + 'T00:00:00.000Z',
          lte: tomorrowEnd.toISOString().split('T')[0] + 'T23:59:59.999Z',
        },
      },
      orderBy: { createdAt: 'asc' },
      include: {
        MeetingUser_Meeting_customerIdToMeetingUser: true,
        MeetingUser_Meeting_hostIdToMeetingUser: true,
      },
    });

    for (const meeting of meetings) {
      this.notificationQueue.add(
        NotificationTask.REMINDER_EMAIL_NOTIFICATION_TASK,
        {
          meeting: meeting,
        },
        {
          jobId: `reminderEmail-${meeting.meetingId}`,
        },
      );
    }
  }

  @Interval(30 * 60 * 1000)
  async reminderThreeHoursBeforeMeeting(): Promise<void> {
    const now = new Date();
    const threeHoursFromNow = new Date(now.getTime() + 3 * 60 * 60 * 1000);
    const meetings = await this.prismaService.meeting.findMany({
      where: {
        status: MeetingStatus.SUCCESSFULLY_SCHEDULED,
        startTime: {
          gte:
            now.toISOString().split('T')[0] +
            'T' +
            now.toTimeString().split(' ')[0] +
            '.000Z',
          lte:
            threeHoursFromNow.toISOString().split('T')[0] +
            'T' +
            threeHoursFromNow.toTimeString().split(' ')[0] +
            '.999Z',
        },
      },
      orderBy: { createdAt: 'asc' },
      include: {
        MeetingUser_Meeting_customerIdToMeetingUser: true,
        MeetingUser_Meeting_hostIdToMeetingUser: true,
      },
    });

    for (const meeting of meetings) {
      this.notificationQueue.add(
        NotificationTask.REMINDER_EMAIL_NOTIFICATION_TASK,
        {
          meeting: meeting,
        },
        {
          jobId: `reminderEmailBefore3Hours-${meeting.meetingId}`,
        },
      );
    }
  }

  @Interval(10 * 1000)
  async contactSupportNotification(): Promise<void> {
    const supportRequests = await this.prismaService.contactSupport.findMany({
      where: {
        status: ContactSupportStatus.PENDING,
      },
      orderBy: { createdAt: 'asc' },
    });

    for (const supportRequest of supportRequests) {
      this.notificationQueue.add(
        NotificationTask.CONTACT_SUPPORT_NOTIFICATION_TASK,
        {
          contactSupport: supportRequest,
        },
        {
          jobId: `contactSupport-${supportRequest.contactSupportId}`,
        },
      );
    }
  }
}
