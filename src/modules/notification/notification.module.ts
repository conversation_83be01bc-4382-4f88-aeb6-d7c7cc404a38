import { Modu<PERSON> } from '@nestjs/common';
import { BullModule } from '@nestjs/bull';

import { NotificationService } from './notification.service';
import { NotificationController } from './notification.controller';
import { TaskNotificationConsumer } from './notification.consumer';
import { PrismaModule } from '../prisma/prisma.module';
import { MailModule } from '../mail/mail.module';
import { AwsModule } from '../aws/aws.module';
import { QueueName } from 'src/utils/enums';
import { SlackModule } from '../slack/slack.module';

@Module({
  imports: [
    MailModule,
    SlackModule,
    AwsModule,
    PrismaModule,
    BullModule.registerQueue({
      name: QueueName.NOTIFICATION_QUEUE,
    }),
  ],
  controllers: [NotificationController],
  providers: [NotificationService, TaskNotificationConsumer],
  exports: [NotificationService],
})
export class NotificationModule {}
