import { BadRequestException, Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import * as SendGrid from '@sendgrid/mail';
import { SendMailDto } from './dto';

type EmailBody = {
  to: string;
  subject: string;
  text: string;
  html: string;
};

type DynamicEmailBody = {
  to: string;
  templateId: string;
  params: Record<string, any>;
};

@Injectable()
export class MailService {
  private readonly MAIL_FROM: string;
  private readonly MAIL_CC: string[];
  private readonly MAIL_BCC: string[];
  constructor(private readonly configService: ConfigService) {
    SendGrid.setApiKey(this.configService.get<string>('SENDGRID_API_KEY'));
    this.MAIL_FROM = this.configService.get<string>('MAIL_FROM');
    this.MAIL_CC = this.configService.get<string>('MAIL_CC').split(',');
    this.MAIL_BCC = this.configService.get<string>('MAIL_BCC').split(',');
  }
  async sendEmail({ to, subject, text, html }: EmailBody): Promise<any> {
    const msg = {
      to,
      bcc: this.MAIL_BCC.filter((email) => email !== to),
      cc: this.MAIL_CC.filter((email) => email !== to),
      from: this.MAIL_FROM,
      subject,
      text,
      html,
    };

    try {
      const res = await SendGrid.send(msg);
      return { data: res };
    } catch (error) {
      console.error(error);

      if (error.response) {
        console.error(error.response.body);
      }
      throw new BadRequestException('Email sending failed.');
    }
  }

  async sendDynamicEmail({
    to,
    templateId,
    params,
  }: DynamicEmailBody): Promise<any> {

    const msg = {
      to,
      bcc: this.MAIL_BCC.filter((email) => email !== to),
      cc: this.MAIL_CC.filter((email) => email !== to),
      from: this.MAIL_FROM,
      templateId, 
      dynamic_template_data: params,
    };

    try {
      const res = await SendGrid.send(msg);
      return { data: res };
    } catch (error) {
      console.error(error);

      if (error.response) {
        console.error(error.response.body);
      }
      throw new BadRequestException('Email sending failed.');
    }
  }
}
