import {
  Controller,
  Post,
  Body,
  UsePipes,
  ValidationPipe,
} from '@nestjs/common';
import { MailService } from './mail.service';
import { SendMailDto } from './dto';

@Controller('mail')
export class MailController {
  constructor(private mailService: MailService) {}

  @Post('send')
  @UsePipes(new ValidationPipe({ transform: true }))
  async sendMail(@Body() sendMailDto: SendMailDto) {
    const { to, text } = sendMailDto;
    const subject = 'Test mail';
    const name = sendMailDto.to;
    const link = 'https://jobs-staging.qlay.ai';
    const html = `<head>
      <title>Test mail</title>
      <style>
        .message-content {
          font-size: 13px;
          color: #333;
          border: 2px solid #eaeaea;
          padding: 10px;
          background-color: #f9f9f9;
          border-radius: 5px;
        }
      </style>
    </head>
    <body>
      <div>
        <p>レポートが完了したので、ご確認ください <strong></strong></p>
      </div>
    </body>`;
    return await this.mailService.sendEmail({ to, subject, text, html });
  }
}
