import { Processor, Process, OnQueueCompleted } from '@nestjs/bull';
import { Job } from 'bull';
import { OAuth2Client } from 'google-auth-library';
import { google } from 'googleapis';
import * as fs from 'fs';

import { QueueName, BookingDemoTask } from './../../utils/enums';
import {
  ITaskBookingDemoConsumer,
  IBookingDemo,
} from './../../utils/interfaces/booking-demo.interface';
import { PrismaService } from '../prisma/prisma.service';
import {
  GoogleCredentials,
  GoogleToken,
  BusyTime,
} from 'src/utils/types/booking-demo.type';

@Processor(QueueName.BOOKING_DEMO_QUEUE)
export class TaskBookingDemoConsumer implements ITaskBookingDemoConsumer {
  constructor(private prismaService: PrismaService) {}
  private readonly WORKER_URL = process.env.WORKER_URL;
  private readonly PREFIX_CALENDAR_ID = process.env.PREFIX_CALENDAR_ID;
  private oAuth2Client: OAuth2Client;
  private readonly SCOPES = ['https://www.googleapis.com/auth/calendar'];
  private readonly TOKEN_PATH = 'token.json';
  private readonly CREDENTIALS_PATH = 'credentials.json';
  private readonly CALENDAR_ID: string[] = process.env.CALENDAR_ID.split(',');
  private readonly EMAIL_HOST_ID = process.env.EMAIL_HOST_ID;

  @Process({ name: BookingDemoTask.SYNC_EVENT_TASK, concurrency: 1 })
  async procesSyncEvents(job: Job<IBookingDemo>): Promise<void> {
    // read credentials file
    console.log('Start processing job...', job.data.salePersons);
    const credentials = fs.readFileSync(this.CREDENTIALS_PATH);
    const parsedCredentials: GoogleCredentials = JSON.parse(
      credentials.toString('utf8'),
    );
    const token = fs.readFileSync(this.TOKEN_PATH);
    const parsedToken: GoogleToken = JSON.parse(token.toString('utf8'));
    const { client_id, client_secret, redirect_uris } =
      parsedCredentials.installed;
    try {
      this.oAuth2Client = new google.auth.OAuth2(
        client_id,
        client_secret,
        redirect_uris[0],
      );
      this.oAuth2Client.setCredentials(parsedToken);
      const tokens = await this.oAuth2Client.refreshAccessToken();
      fs.writeFileSync(this.TOKEN_PATH, JSON.stringify(tokens.res.data));

      const calendar = google.calendar({
        version: 'v3',
        auth: this.oAuth2Client,
      });
      const calendarList = await calendar.calendarList.list();
      for (const item of calendarList.data.items) {
        if (this.CALENDAR_ID.includes(item.id)) {
          const meetingUserId = job.data.salePersons.filter(
            (saler) => saler.workEmail == item.id,
          )[0].meetingUserId;
          let pageToken: string | undefined = '1';
          // Set the time range from now to 10 days from now
          const timeMin = new Date().toISOString();
          const timeMax = new Date(
            new Date().getTime() + 2 * 30 * 24 * 60 * 60 * 1000, // 2 months
          ).toISOString();
          const times: BusyTime[] = [];

          while (pageToken) {
            const events = await calendar.events.list({
              calendarId: item.id,
              maxResults: 10,
              timeMin,
              timeMax,
              singleEvents: true,
              orderBy: 'startTime',
              timeZone: 'UTC',
              pageToken: pageToken == '1' ? undefined : pageToken,
            });
            for (const event of events.data.items) {
              if (event.start) {
                const startTime = new Date(
                  event.start.dateTime || event.start.date,
                ).toISOString();
                const endTime = new Date(
                  event.end.dateTime || event.end.date,
                ).toISOString();

                times.push({
                  startTime,
                  endTime,
                  meetingUserId,
                });
                const isExistBusyTime =
                  await this.prismaService.busyTime.findFirst({
                    where: {
                      startTime,
                      endTime,
                      meetingUserId,
                      isActive: true,
                    },
                  });
                if (!isExistBusyTime) {
                  await this.prismaService.busyTime.create({
                    data: {
                      startTime,
                      endTime,
                      meetingUserId,
                      isActive: true,
                    },
                  });
                  console.log(`${item.summary} start time: `, startTime);
                  console.log(`${item.summary} end time: `, endTime);
                }
              }
            }
            pageToken = events.data.nextPageToken;
          }
          // filter invalid busy time
          const currentBusyTimes = await this.prismaService.busyTime.findMany({
            where: {
              meetingUserId,
              startTime: {
                gte: new Date().toISOString(),
                lt: new Date(
                  new Date().getTime() + 2 * 30 * 24 * 60 * 60 * 1000,
                ).toISOString(),
              },
              isActive: true,
            },
            select: {
              busyTimeId: true,
              startTime: true,
              endTime: true,
            },
          });

          for (const busyTime of currentBusyTimes) {
            if (
              !times.find(
                (time) =>
                  time.startTime == busyTime.startTime.toISOString() &&
                  time.endTime == busyTime.endTime.toISOString(),
              )
            ) {
              await this.prismaService.busyTime.update({
                where: {
                  busyTimeId: busyTime.busyTimeId,
                },
                data: {
                  isActive: false,
                },
              });
              console.log(
                `${item.summary} time have been deleted: ${busyTime.startTime.toISOString()} - ${busyTime.endTime.toISOString()}`,
              );
            }
          }
          // try {
          //   // watch for changes
          //   const watchRequest = {
          //     id: `${meetingUserId}${this.PREFIX_CALENDAR_ID}`, // Unique ID for each channel
          //     type: 'web_hook',
          //     address: `${this.WORKER_URL}/api/v1/booking-demo/watch`, // Your webhook endpoint
          //   };
          //   const watch = await calendar.events.watch({
          //     calendarId: item.id,
          //     requestBody: watchRequest,
          //   });
          //   console.log(watch.data);
          // } catch (error) {
          //   // console.log(error, 'error');
          // }
        }
      }
    } catch (error) {
      console.log(error, 'error');
    }
  }

  // get events
  @Process({ name: BookingDemoTask.GET_EVENT_TASK, concurrency: 1 })
  async processGetEvents(job: Job<IBookingDemo>): Promise<any> {
    const { salePerson } = job.data;
    // read credentials file
    const credentials = fs.readFileSync(this.CREDENTIALS_PATH);
    const parsedCredentials: GoogleCredentials = JSON.parse(
      credentials.toString('utf8'),
    );
    const token = fs.readFileSync(this.TOKEN_PATH);
    const parsedToken: GoogleToken = JSON.parse(token.toString('utf8'));
    const { client_id, client_secret, redirect_uris } =
      parsedCredentials.installed;
    try {
      this.oAuth2Client = new google.auth.OAuth2(
        client_id,
        client_secret,
        redirect_uris[0],
      );
      this.oAuth2Client.setCredentials(parsedToken);
      const tokens = await this.oAuth2Client.refreshAccessToken();
      fs.writeFileSync(this.TOKEN_PATH, JSON.stringify(tokens.res.data));

      const calendar = google.calendar({
        version: 'v3',
        auth: this.oAuth2Client,
      });

      const times = [];
      let pageToken: string | undefined = '1';
      // Set the time range from now to 10 days from now
      const timeMin = new Date().toISOString();
      const timeMax = new Date(
        new Date().getTime() + 2 * 30 * 24 * 60 * 60 * 1000, // 2 months
      ).toISOString();
      while (pageToken) {
        const events = await calendar.events.list({
          calendarId: salePerson.workEmail,
          maxResults: 10,
          timeMin,
          timeMax,
          singleEvents: true,
          orderBy: 'startTime',
          timeZone: 'UTC',
          pageToken: pageToken == '1' ? undefined : pageToken,
        });

        for (const event of events.data.items) {
          if (event.start) {
            const startTime = new Date(
              event.start.dateTime || event.start.date,
            ).toISOString();
            const endTime = new Date(
              event.end.dateTime || event.end.date,
            ).toISOString();

            console.log(`${salePerson.workEmail} start time: `, startTime);
            console.log(`${salePerson.workEmail} end time: `, endTime);
            times.push({
              startTime,
              endTime,
            });
          }
        }
        pageToken = events.data.nextPageToken;
      }

      return times;
    } catch (error) {
      console.log(error, 'error');
    }
  }
  @OnQueueCompleted()
  completed(job: Job) {
    console.log(`Job ${job.id} has completed!`);
  }
}
