import { Modu<PERSON> } from '@nestjs/common';
import { BullModule } from '@nestjs/bull';
import * as path from 'path';

import { BookingDemoController } from './booking-demo.controller';
import { TaskBookingDemoConsumer } from './booking-demo.consumer';
import { BookingDemoService } from './booking-demo.service';
import { QueueName } from './../../utils/enums';
import { PrismaModule } from '../prisma/prisma.module';

@Module({
  imports: [
    PrismaModule,
    BullModule.registerQueue({
      name: QueueName.BOOKING_DEMO_QUEUE,
    }),
  ],
  controllers: [BookingDemoController],
  providers: [TaskBookingDemoConsumer, BookingDemoService],
  exports: [],
})
export class BookingDemoModule {}
