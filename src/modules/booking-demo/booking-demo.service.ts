import { Injectable } from '@nestjs/common';
import { Interval } from '@nestjs/schedule';
import { InjectQueue } from '@nestjs/bull';
import { Queue } from 'bull';

import { QueueName, BookingDemoTask } from './../../utils/enums';
import { IBookingDemoService } from './../../utils/interfaces/booking-demo.interface';
import { PrismaService } from '../prisma/prisma.service';
import { IBookingDemo } from './../../utils/interfaces/booking-demo.interface';

@Injectable()
export class BookingDemoService implements IBookingDemoService {
  constructor(
    @InjectQueue(QueueName.BOOKING_DEMO_QUEUE)
    private bookingDemoQueue: Queue<IBookingDemo>,
    private prismaService: PrismaService,
  ) {}
  private readonly PREFIX_CALENDAR_ID = process.env.PREFIX_CALENDAR_ID;
  @Interval(10 * 60 * 1000)
  async syncEvents(): Promise<void> {
    const salePersons = await this.prismaService.meetingUser.findMany({
      where: {
        isHost: true,
      },
    });

    if (salePersons.length > 0)
      this.bookingDemoQueue.add(
        BookingDemoTask.SYNC_EVENT_TASK,
        {
          salePersons,
        },
        {
          jobId: `syncEvents-${new Date().getTime()}`,
          removeOnComplete: true,
        },
      );
  }

  // async watchEvents(channelId: string): Promise<void> {
  //   const salePerson = await this.prismaService.meetingUser.findFirst({
  //     where: {
  //       isHost: true,
  //       meetingUserId: Number(channelId.split(this.PREFIX_CALENDAR_ID)[0]),
  //     },
  //   });
  // }

  async getEvents(salePersonId: number): Promise<void> {
    const salePerson = await this.prismaService.meetingUser.findFirst({
      where: {
        meetingUserId: salePersonId,
      },
    });
    const job = await this.bookingDemoQueue.add(
      BookingDemoTask.GET_EVENT_TASK,
      {
        salePerson,
      },
      {
        jobId: `getEvents-${salePerson.meetingUserId}`,
        removeOnComplete: true,
      },
    );

    return await job.finished();
  }
}
