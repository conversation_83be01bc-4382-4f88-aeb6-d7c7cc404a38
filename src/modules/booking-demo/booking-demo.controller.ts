import { Controller, Headers, Post, Get, Body } from '@nestjs/common';

import { BookingDemoService } from './booking-demo.service';

@Controller('booking-demo')
export class BookingDemoController {
  constructor(private bookingDemoService: BookingDemoService) {}
  @Post('watch')
  watch(@Headers() headers: Record<string, string>) {
    return this.bookingDemoService.syncEvents();
  }

  @Get('events')
  async getEvents(@Body() body: { salePersonId: number }) {
    const { salePersonId } = body;
    return await this.bookingDemoService.getEvents(salePersonId);
  }
}
