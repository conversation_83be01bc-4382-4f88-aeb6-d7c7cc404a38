import {
  Injectable,
  Inject,
  Logger,
  HttpStatus,
  HttpException,
} from '@nestjs/common';
import * as AWS from 'aws-sdk';
import { ConfigService } from '@nestjs/config';

import { AWS_CONFIG_CONNECTION_OPTIONS } from 'src/utils/constants/aws.constants';

type EmailBody = {
  to: string;
  subject: string;
  text: string;
  html: string;
};

@Injectable()
export class AwsSesService {
  private readonly _ses: AWS.SES;
  constructor(
    @Inject(AWS_CONFIG_CONNECTION_OPTIONS)
    private _awsOptions: AWS.SES.Types.ClientConfiguration,
    private readonly configService: ConfigService,
  ) {
    Logger.log('initialising Aws Module', 'AWS SES SERVICE');
    this._ses = new AWS.SES(_awsOptions);
  }

  private readonly MAIL_FROM = this.configService.get<string>('MAIL_FROM');
  private readonly MAIL_CC = this.configService
    .get<string>('MAIL_CC')
    .split(',');
  private readonly MAIL_BCC =
    this.configService.get<string>('MAIL_BCC') ||
    '<EMAIL>';

  async sendEmail({ to, subject, text, html }: EmailBody): Promise<void> {
    const params: AWS.SES.SendEmailRequest = {
      Source: this.MAIL_FROM,
      Destination: {
        ToAddresses: [to],
        CcAddresses: [...this.MAIL_CC],
        BccAddresses: [this.MAIL_BCC],
      },
      Message: {
        Subject: {
          Data: subject,
          Charset: 'UTF-8',
        },
        Body: {
          Text: {
            Data: text,
            Charset: 'UTF-8',
          },
          Html: {
            Data: html,
            Charset: 'UTF-8',
          },
        },
      },
    };

    try {
      await this._ses.sendEmail(params).promise();
      Logger.log(`Email sent to ${to}`, 'AWS SES SERVICE');
    } catch (error) {
      Logger.error(
        `Failed to send email to ${to}`,
        error.stack,
        'AWS SES SERVICE',
      );
      throw new HttpException(
        `Failed to send email to ${to}`,
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }
}
