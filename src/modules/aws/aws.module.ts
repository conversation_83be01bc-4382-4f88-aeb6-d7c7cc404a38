import { Module } from '@nestjs/common';
import { AwsSesService } from './aws.ses.service';
import { AwsSesController } from './aws.ses.controller';
import { AWS_CONFIG_CONNECTION_OPTIONS } from 'src/utils/constants/aws.constants';

@Module({
  controllers: [AwsSesController],
  providers: [
    {
      provide: AWS_CONFIG_CONNECTION_OPTIONS,
      useValue: {
        region: process.env.AWS_REGION,
        accessKeyId: process.env.AWS_ACCESS_KEY_ID,
        secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY,
      },
    },
    AwsSesService,
  ],
  exports: [AwsSesService],
})
export class AwsModule {}
