import { Module, MiddlewareConsumer } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { ScheduleModule } from '@nestjs/schedule';
import { BullModule } from '@nestjs/bull';

import { AppController } from './app.controller';
import { AppService } from './app.service';
import { LoggerMiddleware } from './middlewares/logger.middleware';
import { GLOBAL_CONFIG } from './configs/global.config';
import { BULL_CONFIG } from './configs/bull.config';
import { LoggerModule } from './modules/logger/logger.module';
import { ApplicationModule } from './modules/application/application.module';
import { HandleScoringModule } from './modules/handle-scoring/handle-scoring.module';
import { ApplicationTestModule } from './modules/application-test/application-test.module';
import { PrismaModule } from './modules/prisma/prisma.module';
import { NotificationModule } from './modules/notification/notification.module';
import { MailModule } from './modules/mail/mail.module';
import { TestScoringModule } from './modules/test-scoring/test-scoring.module';
import { AwsModule } from './modules/aws/aws.module';
import { SlackModule } from './modules/slack/slack.module';
import { BookingDemoModule } from './modules/booking-demo/booking-demo.module';
import { CvScanningModule } from './modules/cv-scanning/cv-scanning.module';
import { MasterFormModule } from './modules/master-form/master-form.module';
import { ProfileSyncModule } from './modules/profile-sync/profile-sync.module';

@Module({
  imports: [
    LoggerModule,
    ConfigModule.forRoot({ isGlobal: true, load: [() => GLOBAL_CONFIG] }),
    PrismaModule,
    BullModule.forRoot({ redis: BULL_CONFIG }),
    ApplicationModule,
    ScheduleModule.forRoot(),
    ApplicationTestModule,
    NotificationModule,
    MailModule,
    TestScoringModule,
    AwsModule,
    SlackModule,
    BookingDemoModule,
    CvScanningModule,
    MasterFormModule,
    ProfileSyncModule,
    HandleScoringModule
  ],
  controllers: [AppController],
  providers: [AppService],
})
export class AppModule {
  configure(consumer: MiddlewareConsumer) {
    consumer.apply(LoggerMiddleware).forRoutes('*');
  }
}
