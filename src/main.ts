import { HttpAdapterHost, NestFactory } from '@nestjs/core';
import { ConfigService } from '@nestjs/config';
import { DocumentBuilder, SwaggerModule } from '@nestjs/swagger';
import { ValidationPipe } from '@nestjs/common';
import * as bodyParser from 'body-parser';
import { ExpressAdapter } from '@bull-board/express';
import { createBullBoard } from '@bull-board/api';
import { BullAdapter } from '@bull-board/api/bullAdapter';
import { Queue } from 'bull';
import * as basicAuth from 'express-basic-auth';

import { AppModule } from './app.module';
import { API_PREFIX } from './utils/constants/global.constants';
import { SwaggerConfig } from './configs/config.interface';
import { GLOBAL_CONFIG } from './configs/global.config';
import { MyLogger } from './modules/logger/logger.service';
import { InvalidFormExceptionFilter } from './utils/filters/invalid.form.exception.filter';
import { AllExceptionsFilter } from './utils/filters/all.exceptions.filter';
import { TransformInterceptor } from './utils/interceptors/transform.interceptor';
import { QueueName } from './utils/enums';
import { SentryExceptionFilter } from './utils/filters/sentry.exception.filter';
import { initSentry } from './configs/sentry.config';


async function bootstrap() {
  const app = await NestFactory.create(AppModule, {
    logger: new MyLogger(),
  });

  initSentry();

  // Bull Board
  const serverAdapter = new ExpressAdapter();
  const bullBoardBasePath = '/admin/queues/dashboard';

  serverAdapter.setBasePath(bullBoardBasePath);
  createBullBoard({
    queues: [
      new BullAdapter(
        app.get<Queue>(`BullQueue_${QueueName.APPLICATION_QUEUE}`),
      ),
      new BullAdapter(
        app.get<Queue>(`BullQueue_${QueueName.APPLICATION_TEST_QUEUE}`),
      ),
      new BullAdapter(
        app.get<Queue>(`BullQueue_${QueueName.NOTIFICATION_QUEUE}`),
      ),
      new BullAdapter(
        app.get<Queue>(`BullQueue_${QueueName.TEST_SCORING_QUEUE}`),
      ),
      new BullAdapter(
        app.get<Queue>(`BullQueue_${QueueName.BOOKING_DEMO_QUEUE}`),
      ),
      new BullAdapter(
        app.get<Queue>(`BullQueue_${QueueName.CV_SCANNING_QUEUE}`),
      ),
      new BullAdapter(
        app.get<Queue>(`BullQueue_${QueueName.MASTER_FORM_QUEUE}`),
      ),
      new BullAdapter(
        app.get<Queue>(`BullQueue_${QueueName.SCORING_HANDLING_QUEUE}`),
      ),
    ],
    serverAdapter,
  });

  app.use(
    bullBoardBasePath,
    basicAuth({
      challenge: true,
      users: { qlay_admin: process.env.BULL_BOARD_PASS },
    }),
  );
  app.use(bullBoardBasePath, serverAdapter.getRouter());

  // Middlewares
  app.use(bodyParser.json({ limit: '100mb' }));
  app.use(bodyParser.urlencoded({ limit: '100mb', extended: true }));

  app.enableCors({
    origin: '*',
    methods: ['GET', 'POST', 'PUT', 'PATCH', 'DELETE'],
  });

  app.setGlobalPrefix(API_PREFIX);

  app.useGlobalFilters(
    new SentryExceptionFilter(),
    new AllExceptionsFilter(app.get(HttpAdapterHost)),
    new InvalidFormExceptionFilter(),
  );

  app.useGlobalPipes(new ValidationPipe());
  app.useGlobalInterceptors(new TransformInterceptor());

  const configService = app.get<ConfigService>(ConfigService);
  const swaggerConfig = configService.get<SwaggerConfig>('swagger');

  // Swagger Api
  if (swaggerConfig.enabled) {
    const options = new DocumentBuilder()
      .setTitle(swaggerConfig.title || 'Nestjs')
      .setDescription(swaggerConfig.description || 'The nestjs API description')
      .setVersion(swaggerConfig.version || '1.0')
      .addBearerAuth()
      .build();
    const document = SwaggerModule.createDocument(app, options);

    SwaggerModule.setup(swaggerConfig.path || 'api', app, document);
  }

  const PORT = process.env.PORT || GLOBAL_CONFIG.nest.port;
  await app.listen(PORT, async () => {
    const myLogger = await app.resolve(MyLogger);
    myLogger.log(`Server started listening: ${PORT}`);
  });
}
bootstrap();
