export function normalizeDate(input: string): string | null {
  try {
    if (!input) return null;
    const lower = input.trim().toLowerCase();
    if (lower === 'present') return null;

    let date: Date | null = null;

    // Match full date
    if (/^\d{4}-\d{2}-\d{2}$/.test(input)) {
      date = new Date(`${input}T00:00:00Z`);
    }
    // Match year-month
    else if (/^\d{4}-\d{2}$/.test(input)) {
      date = new Date(`${input}-01T00:00:00Z`);
    }
    // Match year only
    else if (/^\d{4}$/.test(input)) {
      date = new Date(`${input}-01-01T00:00:00Z`);
    }

    if (date && !isNaN(date.getTime())) {
      return date.toISOString();
    }
    // Invalid date format
    return null;
  } catch (error) {
    console.log(`Error for date ${input}: `, error.message);
    return null;
  }
}
