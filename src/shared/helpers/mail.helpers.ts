import { Prisma } from '@prisma/client';

import {
  INotification,
  ICVTaskCandidate,
} from 'src/utils/interfaces/notification.interface';
import { EmailType, EmailStructure, Language } from 'src/utils/enums';
import {
  getEnvironmentLink,
  getEnvironmentLandingLink,
} from 'src/utils/constants/global.constants';
import { CandidateDetail } from 'src/utils/types/cv-scanning.type';

const companyName = '© 2024 Qlay Technologies, Inc. All rights reserved.';

export function getUserName(profile: any): string {
  if (!profile || profile.length === 0) {
    return 'No Name';
  }
  const { firstName, lastName } = profile;
  return `${firstName} ${lastName}`;
}

function formatMeetingTime(
  startDate: Date,
  endDate: Date,
  timeZone: string,
): string {
  if (!timeZone) timeZone = 'UTC';
  const dateOptions: Intl.DateTimeFormatOptions = {
    weekday: 'long',
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    timeZone: timeZone, // Use dynamic time zone
  };

  const timeOptions: Intl.DateTimeFormatOptions = {
    hour: 'numeric',
    minute: 'numeric',
    hour12: true,
    timeZone: timeZone, // Use dynamic time zone
  };

  const formattedStartDate = startDate.toLocaleDateString('en-US', dateOptions);
  const formattedStartTime = startDate.toLocaleTimeString('en-US', timeOptions);
  const formattedEndTime = endDate.toLocaleTimeString('en-US', timeOptions);

  // Get a user-friendly time zone name
  const timeZoneName = new Intl.DateTimeFormat('en-US', {
    timeZone,
    timeZoneName: 'long',
  })
    .formatToParts()
    .find((part) => part.type === 'timeZoneName')?.value;

  return `${formattedStartDate} • ${formattedStartTime} - ${formattedEndTime} (${timeZoneName})`;
}

function getEmailHtml(
  notificationParams: INotification,
  language: Language,
  type: EmailType,
): string {
  let job,
    userRole,
    applicationId,
    userName,
    jobTitle,
    testDashboardLink: string = '';
  const companyName = '© 2025 Qlay Technologies, Inc. All rights reserved.';

  if (notificationParams.foundationTest) {
    job = notificationParams.foundationTest.application.job;
    userRole = notificationParams.foundationTest.application.userRole;
    applicationId = notificationParams.foundationTest.application.applicationId;

    userName = getUserName(userRole.profile);
    jobTitle = job.title;
    testDashboardLink = `${getEnvironmentLink()}/all-tests/${applicationId}/details`;
  }

  // Schedule Meeting
  //   cancel: http://localhost:3000/request-demo/cancel/${meetingId}
  // reschedule: http://localhost:3000/request-demo/reschedule?rescheduleId=${meetingId}
  let rescheduleLink,
    cancelLink,
    meeting,
    formattedMeetingTime,
    meetingHostName,
    meetingClientName,
    meetingClientEmail,
    meetingLink;
  if (notificationParams.meeting) {
    meeting = notificationParams.meeting;
    meetingHostName =
      notificationParams.meeting.MeetingUser_Meeting_hostIdToMeetingUser
        .firstName;
    meetingClientName =
      notificationParams.meeting.MeetingUser_Meeting_customerIdToMeetingUser
        .firstName;
    formattedMeetingTime = formatMeetingTime(
      new Date(meeting.startTime),
      new Date(meeting.endTime),
      meeting.timezone,
    );
    meetingClientEmail =
      meeting.MeetingUser_Meeting_customerIdToMeetingUser.workEmail;
    meetingLink = meeting.meetingLink
      ? meeting.meetingLink
      : meeting.linkMeeting;
    rescheduleLink = `${getEnvironmentLandingLink()}/request-demo/reschedule?rescheduleId=${meeting.meetingId}`;
    cancelLink = `${getEnvironmentLandingLink()}/request-demo/cancel/${meeting.meetingId}`;
  }

  let cvTaskPosition;
  let cvTaskCandidates: ICVTaskCandidate[] = [];
  let cvTaskCurrentDate;
  if (notificationParams.cvTask) {
    cvTaskPosition = notificationParams.cvTask.job.title;
    cvTaskCandidates = notificationParams.cvTask.candidates
      .sort((a, b) => b.score - a.score)
      .slice(0, 10);
    cvTaskCurrentDate = new Date().toLocaleDateString('en-US');
  }

  let contactSupportName;
  let contactSupportEmail;
  let contactSupportMessage;
  let contactSupportProof;
  if (notificationParams.contactSupport) {
    contactSupportName = notificationParams.contactSupport.fullName;
    contactSupportEmail = notificationParams.contactSupport.email;
    contactSupportMessage = notificationParams.contactSupport.message;
    contactSupportProof = notificationParams.contactSupport.proof;
  }
  const DICTIONARY = {
    [EmailType.FOUNDATION_TEST]: {
      [Language.EN]: `<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Job Application Tests</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            margin: 0;
            padding: 0;
            background-color: #f4f4f4;
        }
        .container {
            width: 80%;
            margin: 0 auto;
            background-color: #ffffff;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
        }
        .header {
            background-color: #6b53ac;
            color: white;
            padding: 10px 20px;
            text-align: center;
            border-radius: 10px 10px 0 0;
        }
        .content {
            padding: 20px;
        }
        .content h1 {
            color: #4CAF50;
        }
        .content p {
            margin: 10px 0;
        }
        .footer {
            margin-top: 20px;
            padding: 10px 20px;
            text-align: center;
            font-size: 12px;
            color: #777;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Complete Your Foundation Test</h1>
        </div>
        <div class="content">
            <p>Dear <strong>${userName}</strong>,</p>
            <p>Thank you for applying for the <strong>${jobTitle}</strong> position at <strong>Qlay Technologies</strong>. We are excited about the possibility of you joining our team.</p>
            <p>As part of our recruitment process, we require candidates to complete three assessments: <strong>AI Interview</strong>, <strong>Technical Test</strong>, and <strong>Live Coding</strong>. These assessments will help us gauge your skills and proficiency in areas crucial for the role.</p>
            <p>Please follow the link below to access the tests:</p>
            <p><strong>Access your tests here</strong>: <a href="${testDashboardLink}">${testDashboardLink}</a></p>
            
            <h2>Instructions:</h2>
            <p>Once you click the link, you will be directed to a platform where you can complete the following assessments:</p>
            <ul>
                <li><strong>AI Interview</strong>: A simulated interview where an AI bot will assess your responses to various technical and behavioral questions.</li>
                <li><strong>Technical Test</strong>: This test evaluates your technical knowledge and problem-solving abilities. Make sure to have a stable internet connection.</li>
                <li><strong>Live Coding</strong>: A real-time coding challenge designed to assess your coding skills and problem-solving abilities under time constraints.</li>
            </ul>
            
            <h2>Important Information:</h2>
            <p>Please complete the tests within one week of receiving this email.</p>
            <p>If you encounter any issues, please contact <NAME_EMAIL>.</p>
            <p>We appreciate your interest and look forward to reviewing your test results.</p>
            <p>Best regards,</p>
            <p>Qlay Recruiting Team</p>
        </div>
        <div class="footer">
            <p>&copy; ${companyName}. All rights reserved.</p>
        </div>
    </div>
</body>
`,
    },
    [EmailType.TEST_RESULT]: {
      [Language.EN]: `<head>
      <meta charset="UTF-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>Job Application Tests</title>
      <style>
          body {
              font-family: Arial, sans-serif;
              line-height: 1.6;
              color: #333;
              margin: 0;
              padding: 0;
              background-color: #f4f4f4;
          }
          .container {
              width: 80%;
              margin: 0 auto;
              background-color: #ffffff;
              padding: 20px;
              border-radius: 10px;
              box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
          }
          .header {
              background-color: #6b53ac;
              color: white;
              padding: 10px 20px;
              text-align: center;
              border-radius: 10px 10px 0 0;
          }
          .content {
              padding: 20px;
          }
          .content h1 {
              color: #4CAF50;
          }
          .content p {
              margin: 10px 0;
          }
          .footer {
              margin-top: 20px;
              padding: 10px 20px;
              text-align: center;
              font-size: 12px;
              color: #777;
          }
      </style>
  </head>
  <body>
      <div class="container">
          <div class="header">
              <h1>Your Test Result</h1>
          </div>
          <div class="content">
            <p>Dear <strong>${userName}</strong>,</p>
            <p>Thank you for participating in our recent interview process with Qlay Technologies. We appreciate the time and effort you invested in this opportunity.</p>
            <p>We have received your interview scores and are currently in the process of reviewing them. We want to ensure a fair evaluation. If your results meet our criteria, we will reach out to you soon to discuss the next steps.</p>
            <p>We appreciate your patience during this review process and look forward to the possibility of continuing our conversation.</p>
              <p><strong>Best regards.</strong></p>
              <p><strong>Qlay Technologies.</strong></p>
          </div>
          <div class="footer">
              <p>&copy; 2024 Qlay Technologies, Inc. All rights reserved.</p>
          </div>
      </div>
  </body>`,
    },
    [EmailType.SCHEDULE_MEETING]: {
      [Language.EN]: `<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>Booking Demo</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      margin: 0;
      padding: 0;
      width: 100%;
    }
    .container {
      margin: 20px auto;
      background-color: #ffffff;
      padding: 20px;
      border-radius: 8px;
      box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
      font-size: 14px;
    }
    .header,
    .content,
    .footer {
      text-align: center;
    }
    .header h2 {
      margin: 0;
    }
    .button {
      background-color: #1a73e8;
      color: #ffffff !important;
      padding: 10px 20px;
      border-radius: 4px;
      text-decoration: none;
      font-size: 16px;
      display: inline-block;
      width: 13rem;
      margin-top: 10px;
      font-weight: 600;
    }
    .link {
      color: #1a73e8;
      text-decoration: none;
    }
    .footer {
      padding: 20px 0;
      color: #888;
    }
    table {
      width: 100%;
      border-collapse: collapse;
    }
    td {
      padding: 10px;
    }
  </style>
</head>
<body>
  <div class="container">
    <!-- Header Section -->
    <div
      class="header"
      style="border: 1px solid #e0d8d8; padding: 1.5rem; margin-bottom: 1.5rem"
    >
      <h2>🚀 ${meetingClientName} & ${meetingHostName} | Qlay Demo</h2>
      <p>${formattedMeetingTime}</p>
    </div>

    <!-- Event Information -->
    <div
      style="border: 1px solid #e0d8d8; border-radius: 10px; padding: 1.5rem"
    >
      <table>
        <tr>
          <td style="width: 60%; vertical-align: top">
            <div style="margin-bottom: 1.5rem">
              <p style="margin-bottom: 1.5rem">Looking forward to a great discussion!</p>
              <p style="margin: 0">
                We’ll use our time to understand your ideal candidate profile and hiring needs to explore how our platform can streamline your recruitment process.
              </p>
            </div>
            <div style="margin-bottom: 1.5rem">
              <p style="margin: 0">
                If you need to reschedule, please use the link below:
              </p>
              <a style="margin: 0" href="${rescheduleLink}" class="link"
                >${rescheduleLink}</a
              >
            </div>
            <div style="margin-bottom: 1.5rem">
              <p style="margin: 0">
                If you need to cancel, please use the link below:
              </p>
              <a style="margin: 0" href="${cancelLink}" class="link"
                >${cancelLink}</a
              >
            </div>
            <div style="margin-bottom: 1.5rem">
              <p style="margin: 0;">
                <strong>When:</strong>
              </p>
              <p style="margin: 0">${formattedMeetingTime}</p>
            </div>
            <div style="margin-bottom: 1.5rem">
              <p style="margin: 0;">
                <strong>Guests:</strong>
              </p>
              <p style="margin: 0">
                ${meetingHostName} -<span style="color: gray"> organizer</span>
              </p>
              <p style="margin: 0">${meetingClientName}</p>
            </div>
          </td>

          <!-- Google Meet Button -->
          <td style="width: 60%; vertical-align: top">
            <a
              href="${meetingLink}"
              class="button"
              style="margin: 0; text-align: center; margin-bottom: 1.5rem"
              >Join with Google Meet</a
            >
            <div style="margin-bottom: 1rem">
              <p style="margin: 0;">
                <strong>Meeting link</strong>
              </p>
              <a href="${meetingLink}" style="margin: 0" class="link"
                >${meetingLink}</a
              >
            </div>
            <div style="margin-bottom: 1rem">
              <p style="margin: 0;">
                <strong>Join by phone</strong>
              </p>
              <p style="margin: 0">(US) ******-404-0793 | PIN: *********</p>
              <a
                style="margin: 0"
                href="https://meet.google.com/phone-numbers"
                class="link"
                >More phone numbers</a
              >
            </div>
          </td>
        </tr>
      </table>
    </div>

    <!-- Footer Section -->
    <div class="footer">
      <p>${companyName}</p>
    </div>
  </div>
</body>
`,
    },
    [EmailType.CANCEL_MEETING]: {
      //       Hi [First name of Client],
      // Thanks for letting us know about the schedule change! No worries at all - these things happen.

      // We're still excited to show you how we can help your business. Whenever you're ready, let’s catch up soon by scheduling a new meeting using below link: [rescheduling link].

      // Looking forward to connecting soon!
      // Best regards,
      // Qlay Team
      [Language.EN]: `<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Job Application Tests</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            margin: 0;
            padding: 0;
            background-color: #f4f4f4;
        }
        .container {
            width: 80%;
            margin: 0 auto;
            background-color: #ffffff;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
        }
        .header {
            background-color: #6b53ac;
            color: white;
            padding: 10px 20px;
            text-align: center;
            border-radius: 10px 10px 0 0;
        }
        .content {
            padding: 20px;
        }
        .content h1 {
            color: #4CAF50;
        }
        .content p {
            margin: 10px 0;
        }
        .footer {
            margin-top: 20px;
            padding: 10px 20px;
            text-align: center;
            font-size: 12px;
            color: #777;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="content">
            <p>Hi ${meetingClientName},</p>
            <p>Thanks for letting us know about the schedule change! No worries at all - these things happen.</p>
            <p>We're still excited to show you how we can help your business. Whenever you're ready, let’s catch up soon by scheduling a new meeting using below link:</p>
            <p><a href="${rescheduleLink}">${rescheduleLink}</a></p>
            <p>Looking forward to connecting soon!</p>
            <p>Best regards,</p>
        </div>
        <div class="footer">
            <p>&copy; ${companyName}. All rights reserved.</p>
        </div>
    </div>
</body>
`,
    },
    [EmailType.CV_SCANNING]: {
      [Language.EN]: `<head>
      <meta charset="UTF-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>Job Application Tests</title>
      <style>
          body {
              font-family: Arial, sans-serif;
              line-height: 1.6;
              color: #333;
              margin: 0;
              padding: 0;
              background-color: #f4f4f4;
          }
          .container {
              width: 80%;
              margin: 0 auto;
              background-color: #ffffff;
              padding: 20px;
              border-radius: 10px;
              box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
          }
          .header {
              background-color: #6b53ac;
              color: white;
              padding: 10px 20px;
              text-align: center;
              border-radius: 10px 10px 0 0;
          }
          .content {
              padding: 20px;
          }
          .content h1 {
              color: #4CAF50;
          }
          .content p {
              margin: 10px 0;
          }
          .footer {
              margin-top: 20px;
              padding: 10px 20px;
              text-align: center;
              font-size: 12px;
              color: #777;
          }
      </style>
  </head>
  <body>
      <div class="container">
          <div class="header">
              <h1>Top Candidates Report - ${cvTaskPosition}</h1>
              <h2>Date: ${cvTaskCurrentDate}</h2>
          </div>
          <div class="content">
            <p style="margin-bottom: 1rem">Below is a summary of our top candidates for the <strong>${cvTaskPosition}</strong> position,</p>
              ${cvTaskCandidates
                .map((candidate, index) => {
                  const candidateDetail =
                    candidate.candidateDetailScanning as CandidateDetail;
                  if (!candidateDetail) return '';
                  return `<div style="margin-bottom: 2rem">
                <p><strong>Top ${index + 1}: ${candidate.Profile.firstName} ${candidate.Profile.lastName} (${candidate.score})</strong></p>
                <p><strong> - Current Position:</strong> N/A</p>
                <p><strong> - Year of experience:</strong> N/A</p>
                <p><strong> - Technical skills:</strong> ${candidateDetail.skills
                  .map((skill) => {
                    return `${skill.skillName}`;
                  })
                  .join(', ')}</p>
                <p><strong> - University:</strong> ${candidate.Profile.academy
                  .map((academy) => {
                    return `${academy.universityName} (${academy.major}): ${new Date(academy.startDate).toLocaleDateString('en-US')} - ${new Date(academy.endDate).toLocaleDateString('en-US')}`;
                  })
                  .join('')}</p>
                <p><strong> - Email:</strong> ${candidate.Profile.email}</p>
                <p><strong> - Phone number:</strong> ${candidate.Profile.phoneNumber}</p>
                </div>`;
                })
                .join('')}
              <p style="margin-top: 3rem"><strong>Best regards.</strong></p>
              <p><strong>Qlay Technologies.</strong></p>
          </div>
          <div class="footer">
              <p>&copy; 2024 Qlay Technologies, Inc. All rights reserved.</p>
          </div>
      </div>
  </body>`,
    },
    [EmailType.REMINDER_EMAIL]: {
      [Language.EN]: `<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>Meeting Reminder</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      margin: 0;
      padding: 0;
      width: 100%;
    }
    .container {
      margin: 20px auto;
      background-color: #ffffff;
      padding: 20px;
      border-radius: 8px;
      box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
      font-size: 14px;
    }
    .header,
    .content,
    .footer {
      text-align: center;
    }
    .header h2 {
      margin: 0;
    }
    .button {
      background-color: #1a73e8;
      color: #ffffff !important;
      padding: 10px 20px;
      border-radius: 4px;
      text-decoration: none;
      font-size: 16px;
      display: inline-block;
      width: 13rem;
      margin-top: 10px;
      font-weight: 600;
    }
    .link {
      color: #1a73e8;
      text-decoration: none;
    }
    .footer {
      padding: 20px 0;
      color: #888;
    }
    table {
      width: 100%;
      border-collapse: collapse;
    }
    td {
      padding: 10px;
    }
  </style>
</head>
<body>
  <div class="container">
    <!-- Header Section -->
    <div
      class="header"
      style="border: 1px solid #e0d8d8; padding: 1.5rem; margin-bottom: 1.5rem"
    >
      <h2>🚀 ${meetingClientName} & ${meetingHostName} | Qlay Demo</h2>
      <p>${formattedMeetingTime}</p>
    </div>

    <!-- Event Information -->
    <div
      style="border: 1px solid #e0d8d8; border-radius: 10px; padding: 1.5rem"
    >
      <table>
        <tr>
          <td style="width: 60%; vertical-align: top">
            <div style="margin-bottom: 1.5rem">
              <p style="margin-bottom: 1.5rem">Great to connect!</p>
              <p style="margin: 0">
                We wanted to send a quick reminder about our upcoming meeting scheduled ${formattedMeetingTime}
              </p>
              <p style="margin: 0">
                We're excited to show you how Qlay can improve your recruiting flow.
              </p>
            </div>
            <div style="margin-bottom: 1.5rem">
              <p style="margin: 0">
                If you need to reschedule, please use the link below:
              </p>
              <a style="margin: 0" href="${rescheduleLink}" class="link"
                >${rescheduleLink}</a
              >
            </div>
            <div style="margin-bottom: 1.5rem">
              <p style="margin: 0">
                If you need to cancel, please use the link below:
              </p>
              <a style="margin: 0" href="${cancelLink}" class="link"
                >${cancelLink}</a
              >
            </div>
            <div style="margin-bottom: 1.5rem">
              <p style="margin: 0;">
                <strong>When:</strong>
              </p>
              <p style="margin: 0">${formattedMeetingTime}</p>
            </div>
            <div style="margin-bottom: 1.5rem">
              <p style="margin: 0;">
                <strong>Guests:</strong>
              </p>
              <p style="margin: 0">
                ${meetingHostName} -<span style="color: gray"> organizer</span>
              </p>
              <p style="margin: 0">${meetingClientName}</p>
            </div>
          </td>

          <!-- Google Meet Button -->
          <td style="width: 40%; vertical-align: top">
            <a
              href="${meetingLink}"
              class="button"
              style="margin: 0; text-align: center; margin-bottom: 1.5rem"
              >Join with Google Meet</a
            >
            <div style="margin-bottom: 1rem">
              <p style="margin: 0;">
                <strong>Meeting link</strong>
              </p>
              <a href="${meetingLink}" style="margin: 0" class="link"
                >${meetingLink}</a
              >
            </div>
            <div style="margin-bottom: 1rem">
              <p style="margin: 0;">
                <strong>Join by phone</strong>
              </p>
              <p style="margin: 0">(US) ******-404-0793 | PIN: *********</p>
              <a
                style="margin: 0"
                href="https://meet.google.com/phone-numbers"
                class="link"
                >More phone numbers</a
              >
            </div>
          </td>
        </tr>
      </table>
    </div>

    <!-- Footer Section -->
    <div class="footer">
      <p>${companyName}</p>
    </div>
  </div>
</body>
`,
    },
    [EmailType.CONTACT_SUPPORT]: {
      [Language.EN]: `<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Job Application Tests</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            margin: 0;
            padding: 0;
            background-color: #f4f4f4;
        }
        .container {
            width: 80%;
            margin: 0 auto;
            background-color: #ffffff;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
        }
        .header {
            background-color: #6b53ac;
            color: white;
            padding: 10px 20px;
            text-align: center;
            border-radius: 10px 10px 0 0;
        }
        .content {
            padding: 20px;
        }
        .content h1 {
            color: #4CAF50;
        }
        .content p {
            margin: 10px 0;
        }
        .footer {
            margin-top: 20px;
            padding: 10px 20px;
            text-align: center;
            font-size: 12px;
            color: #777;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="content">
            <p>Dear HR & Dev team,</p>
            <p>Please help candidate below figure out the problem.</p>
            <p>Full name: <strong>${contactSupportName}</strong></p>
            <p>Email: <strong>${contactSupportEmail}</strong></p>
            <p>Screenshot of error:</p>
            <img style="width: 80%" src="${contactSupportProof}" alt="Proof" />
            <p>Description: ${contactSupportMessage}</p>
            <p>Best regards,</p>
        </div>
        <div class="footer">
            <p>${companyName}.</p>
        </div>
    </div>
</body>
`,
    },
  };
  return DICTIONARY[type][language] || '';
}

export function getTextByLanguageCode(
  type: EmailType,
  emailStructure: EmailStructure,
  language: Language,
  notificationParams: INotification,
): string {
  let formattedMeetingTime, meetingHostName, meetingClientName;
  if (notificationParams.meeting) {
    meetingHostName =
      notificationParams.meeting.MeetingUser_Meeting_hostIdToMeetingUser
        .firstName;
    meetingClientName =
      notificationParams.meeting.MeetingUser_Meeting_customerIdToMeetingUser
        .firstName;
    formattedMeetingTime = formatMeetingTime(
      new Date(notificationParams.meeting.startTime),
      new Date(notificationParams.meeting.endTime),
      notificationParams.meeting.timezone,
    );
  }

  let cvTaskPosition;
  if (notificationParams.cvTask) {
    cvTaskPosition = notificationParams.cvTask.job.title;
  }
  const emailDefaultText = `🚀 ${meetingClientName} & ${meetingHostName} | Qlay Demo @ ${formattedMeetingTime}`;

  let contactSupportName;
  let contactSupportEmail;
  let contactSupportMessage;
  let contactSupportPhone;
  let contactSupportProof;
  if (notificationParams.contactSupport) {
    contactSupportName = notificationParams.contactSupport.fullName;
    contactSupportEmail = notificationParams.contactSupport.email;
    contactSupportMessage = notificationParams.contactSupport.message;
    contactSupportPhone = notificationParams.contactSupport.phoneNumber;
    contactSupportProof = notificationParams.contactSupport.proof;
  }

  const DICTIONARY = {
    // For Foundation Test
    [EmailType.FOUNDATION_TEST]: {
      [EmailStructure.EMAIL_SUBJECT]: {
        [Language.EN]: 'Complete Your Technical and English Tests',
      },
      [EmailStructure.EMAIL_TEXT]: {
        [Language.EN]: 'Test',
      },
      [EmailStructure.EMAIL_TITLE]: {
        [Language.EN]: 'Complete Your Technical and English Tests',
      },
      [EmailStructure.EMAIL_HTML]: {
        [Language.EN]: getEmailHtml(
          notificationParams,
          Language.EN,
          EmailType.FOUNDATION_TEST,
        ),
      },
      [EmailStructure.EMAIL_BUTTON]: {
        [Language.EN]: 'Take the test',
      },
    },
    // For Test Result
    [EmailType.TEST_RESULT]: {
      [EmailStructure.EMAIL_SUBJECT]: {
        [Language.EN]: 'Your Test Result',
      },
      [EmailStructure.EMAIL_TEXT]: {
        [Language.EN]: 'Thank you for taking the time to complete the tests.',
      },
      [EmailStructure.EMAIL_TITLE]: {
        [Language.EN]: 'Your Test Result',
      },
      [EmailStructure.EMAIL_HTML]: {
        [Language.EN]: getEmailHtml(
          notificationParams,
          Language.EN,
          EmailType.TEST_RESULT,
        ),
      },
      [EmailStructure.EMAIL_BUTTON]: {
        [Language.EN]: 'Get Result',
      },
    },
    // For Schedule Meeting
    [EmailType.SCHEDULE_MEETING]: {
      [EmailStructure.EMAIL_SUBJECT]: {
        [Language.EN]: `Invitation: ${emailDefaultText}`,
      },
      [EmailStructure.EMAIL_TEXT]: {
        [Language.EN]: `Invitation: ${emailDefaultText}`,
      },
      [EmailStructure.EMAIL_TITLE]: {
        [Language.EN]: `Invitation: ${emailDefaultText}`,
      },
      [EmailStructure.EMAIL_HTML]: {
        [Language.EN]: getEmailHtml(
          notificationParams,
          Language.EN,
          EmailType.SCHEDULE_MEETING,
        ),
      },
      [EmailStructure.EMAIL_BUTTON]: {
        [Language.EN]: 'Schedule Meeting',
      },
    },
    // For Cancel Meeting
    [EmailType.CANCEL_MEETING]: {
      [EmailStructure.EMAIL_SUBJECT]: {
        [Language.EN]: `Meeting Canceled: ${emailDefaultText}`,
      },
      [EmailStructure.EMAIL_TEXT]: {
        [Language.EN]: `Meeting Canceled: ${emailDefaultText}`,
      },
      [EmailStructure.EMAIL_TITLE]: {
        [Language.EN]: `Meeting Canceled: ${emailDefaultText}`,
      },
      [EmailStructure.EMAIL_HTML]: {
        [Language.EN]: getEmailHtml(
          notificationParams,
          Language.EN,
          EmailType.CANCEL_MEETING,
        ),
      },
      [EmailStructure.EMAIL_BUTTON]: {
        [Language.EN]: 'Cancel Meeting',
      },
    },
    // For CV Scanning
    [EmailType.CV_SCANNING]: {
      [EmailStructure.EMAIL_SUBJECT]: {
        [Language.EN]: `Top Candidates Report - ${cvTaskPosition}`,
      },
      [EmailStructure.EMAIL_TEXT]: {
        [Language.EN]: `Top Candidates Report - ${cvTaskPosition}`,
      },
      [EmailStructure.EMAIL_TITLE]: {
        [Language.EN]: `Top Candidates Report - ${cvTaskPosition}`,
      },
      [EmailStructure.EMAIL_HTML]: {
        [Language.EN]: getEmailHtml(
          notificationParams,
          Language.EN,
          EmailType.CV_SCANNING,
        ),
      },
      [EmailStructure.EMAIL_BUTTON]: {
        [Language.EN]: 'View Report',
      },
    },
    // For Schedule Meeting
    [EmailType.REMINDER_EMAIL]: {
      [EmailStructure.EMAIL_SUBJECT]: {
        [Language.EN]: `Meeting Reminder: ${emailDefaultText}`,
      },
      [EmailStructure.EMAIL_TEXT]: {
        [Language.EN]: `Meeting Reminder: ${emailDefaultText}`,
      },
      [EmailStructure.EMAIL_TITLE]: {
        [Language.EN]: `Meeting Reminder: ${emailDefaultText}`,
      },
      [EmailStructure.EMAIL_HTML]: {
        [Language.EN]: getEmailHtml(
          notificationParams,
          Language.EN,
          EmailType.REMINDER_EMAIL,
        ),
      },
      [EmailStructure.EMAIL_BUTTON]: {
        [Language.EN]: 'Schedule Meeting',
      },
    },
    // For Contact Support
    [EmailType.CONTACT_SUPPORT]: {
      [EmailStructure.EMAIL_SUBJECT]: {
        [Language.EN]: `⚠  [candidate] Support request: ${contactSupportName} - ${contactSupportEmail}`,
      },
      [EmailStructure.EMAIL_TEXT]: {
        [Language.EN]: `⚠  [candidate] Support request: ${contactSupportName} - ${contactSupportEmail}`,
      },
      [EmailStructure.EMAIL_TITLE]: {
        [Language.EN]: `⚠  [candidate] Support request: ${contactSupportName} - ${contactSupportEmail}`,
      },
      [EmailStructure.EMAIL_HTML]: {
        [Language.EN]: getEmailHtml(
          notificationParams,
          Language.EN,
          EmailType.CONTACT_SUPPORT,
        ),
      },
      [EmailStructure.EMAIL_BUTTON]: {
        [Language.EN]: 'Contact Support',
      },
    },
  };
  return DICTIONARY[type][emailStructure][language] || '';
}

export function getEmailBookingParams(
  notificationParams: INotification,
): Record<string, any> {

  let rescheduleLink,
    cancelLink,
    meeting,
    formattedMeetingTime,
    meetingHostName,
    meetingClientName,
    meetingClientEmail,
    meetingLink;

  if (notificationParams.meeting) {
    meeting = notificationParams.meeting;
    meetingHostName = notificationParams.meeting.MeetingUser_Meeting_hostIdToMeetingUser.firstName;
    meetingClientName = notificationParams.meeting.MeetingUser_Meeting_customerIdToMeetingUser.firstName;
    formattedMeetingTime = formatMeetingTime(
      new Date(meeting.startTime),
      new Date(meeting.endTime),
      meeting.timezone,
    );
    meetingClientEmail =
      meeting.MeetingUser_Meeting_customerIdToMeetingUser.workEmail;
    meetingLink = meeting.meetingLink ? meeting.meetingLink : meeting.linkMeeting;
    rescheduleLink = `${getEnvironmentLandingLink()}/request-demo/reschedule?rescheduleId=${meeting.meetingId}`;
    cancelLink = `${getEnvironmentLandingLink()}/request-demo/cancel/${meeting.meetingId}`;
  }
  return { "rescheduleLink": rescheduleLink, "cancelLink": cancelLink, "meetingLink": meetingLink, "meetingHostName": meetingHostName, "meetingClientName": meetingClientName, "formattedMeetingTime": formattedMeetingTime, "companyName": companyName };
}

export function getScanCvEmailParams(
  notificationParams: INotification,
): Record<string, any> {
  let cvTaskPosition;
  let cvTaskCandidates: ICVTaskCandidate[] = [];
  let cvTaskCurrentDate;
  let cvTaskCandidatesWithIndex = [];
  if (notificationParams.cvTask) {
    cvTaskPosition = notificationParams.cvTask.job.title;
    cvTaskCandidates = notificationParams.cvTask.candidates
      .sort((a, b) => b.score - a.score)
      .slice(0, 10);
    cvTaskCandidates.forEach((candidate, index) => {
      let candidateWithIndex = {
        ...candidate,
        index: index + 1
      };
      cvTaskCandidatesWithIndex.push(candidateWithIndex);
    });
    cvTaskCurrentDate = new Date().toLocaleDateString('en-US');
  }
  return { "cvTaskPosition": cvTaskPosition, "cvTaskCurrentDate": cvTaskCurrentDate, "cvTaskCandidates": cvTaskCandidatesWithIndex }
}

export function getFoundationTestParams(
  notificationParams: INotification,
): Record<string, any> {

  let job,
    userRole,
    applicationId,
    userName,
    jobTitle,
    testDashboardLink: string = '';

  job = notificationParams.foundationTest.application.job;
  userRole = notificationParams.foundationTest.application.userRole;
  applicationId = notificationParams.foundationTest.application.applicationId;

  userName = getUserName(userRole.profile);
  jobTitle = job.title;
  testDashboardLink = `${getEnvironmentLink()}/all-tests/${applicationId}/details`;
  return { "userName": userName, "jobTitle": jobTitle, "testDashboardLink": testDashboardLink, "companyName": companyName }
}
