export type MasterFormCvScanning = {
  cv_scanned_url?: string;
  summary?: string;
  birthDate?: string;
  emailAddress?: string;
  firstName?: string;
  middleName?: string;
  lastName?: string;
  preferredName?: string;
  phoneNumber?: string;
  profilePhoto?: string;
  academy: {
    universityName?: string;
    major?: string;
    degree?: string;
    degreeStatus?: string;
    gpa?: number;
    startDate?: string;
    endDate?: string;
    proof?: string[];
  }[];
  technicalSkill: {
    skillName?: string;
  }[];
  employment: {
    companyName?: string;
    startDate?: string;
    endDate?: string;
    description?: string[];
    currentlyWorking?: boolean;
    workingPositionHistory: {
      position?: string;
      positionStartDate?: string;
      positionEndDate?: string;
      skills?: string;
    }[];
  }[];
  channel: {
    type?: string;
    platformUrl?: string;
    platform?: {
      platformName?: string;
    }[];
  }[];
  personalProject: {
    projectName?: string;
    startDate?: string;
    endDate?: string;
    workingPosition?: string;
    description?: string[];
  }[];
  award: {
    title?: string;
    date?: string;
  }[];
  publication: {
    title?: string;
    publicationName?: string;
    dateOfPublication?: string;
    link?: string;
    authors?: string[];
    description?: string;
  }[];
  certification: {
    name?: string;
    issueDate?: string;
    proof?: string;
    description?: string;
    type?: string;
  }[];
  language: {
    languageName?: string;
    level?: string;
  }[];
};

export type UserConversationResponse = {
  code: number;
  message: string;
  data: {
    message_id: string;
    sender_id: string;
    content: string;
    created_at: string;
  }[];
};

export type MasterFormEnglishScoringResponse = {
  code: number;
  message: string;
  data: {
    conversation_score: number;
    speaking_score: number;
  };
};

export type MasterFormResumeContent = {
  summary: string;
  birthDate: string;
  emailAddress: string;
  firstName: string;
  middleName: string;
  lastName: string;
  preferredName: string;
  phoneNumber: string;
  profilePhoto: string;
  academy: {
    universityName: string;
    major: string;
    degree: string;
    degreeStatus: string;
    gpa: number;
    startDate: string;
    endDate: string;
    proof: string[];
  }[];
  technicalSkill: {
    skillName: string;
  }[];
  employment: {
    companyName: string;
    startDate: string;
    endDate: string;
    description: string[];
    currentlyWorking: boolean;
    workingPositionHistory: {
      position: string;
      positionStartDate: string;
      positionEndDate: string;
      skills: string;
      employmentType: string;
    }[];
  }[];
  channel: {
    type: string;
    platformUrl: string;
    platform: {
      platformName: string;
    }[];
  }[];
  personalProject: {
    projectName: string;
    startDate: string;
    endDate: string;
    workingPosition: string;
    description: string[];
  }[];
  award: {
    title: string;
    date: string;
  }[];
  publication: {
    title: string;
    publicationName: string;
    dateOfPublication: string;
    link: string;
    authors: string[];
    description: string[];
  }[];
  certification: {
    name: string;
    issueDate: string;
    proof: string;
    description: string;
    type: string;
  }[];
  language: {
    languageName: string;
    level: string;
  }[];
};

export type MasterFormWorkPreferenceContent = {
  workPreferenceContent: {
    email: string;
    citizenship: string[];
    currency: string;
    idealSalary: number;
    taxResidence: string;
    currentSalary: number;
    minimumSalary: number;
    referralSource: string;
    startDateOption: string;
    startDate: string;
    authorizedToWork: boolean;
    physicalLocation: string;
    workAvailability: string;
  };
};

type AcademyHistory = {
  universityName: string;
  major: string;
  degree: string;
  degreeStatus: string;
  gpa: number;
  startDate: string;
  endDate: string;
  proof: string[];
};

type technicalSkill = {
  skillName: string;
};

type Channel = {
  type: string;
  platformUrl: string;
  platform: {
    platformName: string;
  }[];
};

type PersonalProject = {
  projectName: string;
  startDate: string;
  endDate: string;
  workingPosition: string;
  description: string[];
};

type Certification = {
  name: string;
  issueDate: string;
  proof: string;
  description: string;
  type: string;
};

type Language = {
  languageName: string;
  level: string;
};

type Award = {
  title: string;
  date: string;
};

type Publication = {
  title: string;
  publicationName: string;
  dateOfPublication: string;
  link: string;
  authors: string[];
  description: string[];
};

type WorkPreference = {
  citizenship: string[];
  idealSalary: number;
  taxResidence: string;
  currentSalary: number;
  minimumSalary: number;
  referralSource: string;
  startDateOption: string;
  authorizedToWork: boolean;
  physicalLocation: string;
  workAvailability: string;
};

type WorkingPositionHistory = {
  position: string;
  positionStartDate: string;
  positionEndDate: string;
  skills: string;
  employmentType: string;
};

type EmploymentHistory = {
  companyName: string;
  currentlyWorking: boolean;
  startDate: string;
  endDate: string;
  description: string[];
  workingPositionHistories: WorkingPositionHistory[];
};

export type CreateProfile = {
  userId: number;
  // status: string;
  // companyId: number;
  // countryId: number;
  summary: string;
  firstName: string;
  preferredName: string;
  lastName: string;
  middleName?: string;
  birthDate: string;
  phoneNumber: string;
  // address: string;
  email: string[];
  // socialLink: string[];
  academyHistories: AcademyHistory[];
  employmentHistories: EmploymentHistory[];
  // languages: Language[];
  profilePhoto: string;
  // cv: string[];
  // currencyId: number;
  currentSalary: number;
  // expectedSalary: number;
  // minSalary: number;
  // idCard: string[];
  // keywordIdList: number[];
  // newKeywordList: string[];
  // subcontractorRate: number;
  // monthlyRate: number;
  // hourlyRate: number;

  workPreference: WorkPreference;
  userTechnicalSkills: technicalSkill[];
  channel: Channel[];
  personalProject: PersonalProject[];
  award: Award[];
  publication: Publication[];
  certification: Certification[];
  language: Language[];
};

export type MasterFormUserResponse = {
  user: {
    id: number;
    email: string;
    role: number;
  };
  accessToken: string;
  refreshToken: string;
};

export type DailyRecordingResponse = {
  id: string;
  start_ts: number;
  status: string;
  max_participants: number;
  room_name: string;
  duration: number;
  share_token: string;
  s3key: string;
  mtgSessionId: string;
};
