export type ScanningCvResponse = {
  message: string;
  data: {
    first_name: string;
    last_name: string;
    nationality: string;
    birth_date: string;
    email: string;
    phone: string;
    university: string;
    address: string;
    language_skill: string;
    links: string[];
  };
  meta_data?: any;
  s3_url: string;
};

export type CvScoreRequest = {
  jd?: {
    jd_id?: string;
    title: string;
    description: string;
    location: string;
    starting_salary: string;
    ending_salary: string;
    currency_salary: string;
    posted_date: string;
    benefit: string;
    requirement: string;
    responsibility: string;
    jd_url?: string;
  };
  candidate?: {
    candidate_id?: string;
    name: string;
    email: string;
    phone: string;
    university: string;
    language_skill: string;
    address: string;
    birthday: string;
    current_salary: string;
    ideal_salary: string;
    minimum_salary: string;
    skills?: string;
    working_position_histories: {
      company_name: string;
      start_date: string;
      end_date: string;
      position: string;
      description: string;
      skills: string;
    }[];
    academic_histories: {
      university_name: string;
      start_date: string;
      end_date: string;
      degree: string;
      major: string;
    }[];
  };
};

type ScanningCvAcademicHistories = {
  university_name: string;
  start_date: string;
  end_date: string;
  degree: string;
  major: string;
};

export type ScanningCvAcademicHistoriesResponse = {
  message: string;
  data: ScanningCvAcademicHistories[];
  meta_data?: any;
};

export type CandidateDetailResponse = {
  academicHistories: ScanningCvAcademicHistoriesResponse;
  workingPositionHistories: ScanningCvWorkingPositionHistoriesResponse;
  skills: ScanningCvKeywordResponse;
};

type ScanningCvWorkingPositionHistories = {
  company_name: string;
  start_date: string;
  end_date: string;
  position: string;
  description: string;
  skills: string;
};

export type ScanningCvWorkingPositionHistoriesResponse = {
  message: string;
  data: ScanningCvWorkingPositionHistories[];
  meta_data?: any;
};

type ScanningCvSkill = {
  skillName: string;
  id: string;
};

export type ScanningCvKeywordResponse = {
  message: string;
  data: ScanningCvSkill[];
  meta_data?: any;
};

export type ScanningSummaryResponse = {
  message: string;
  data: {
    summary: string | null;
  };
  meta_data?: any;
};

export type ScanningCvPersonalProjectResponse = {
  message: string;
  data: {
    project_name: string;
    start_date: string;
    end_date: string;
    working_position: string;
    description: string;
  }[];
  meta_data?: any;
};

export type ScanningJdResponse = {
  message: string;
  data: {
    title: string;
    description: string;
    location: string;
    starting_salary: string;
    ending_salary: string;
    currency_salary: string;
    posted_date: string;
    benefit: string;
    requirement: string;
    responsibility: string;
  };
  meta_data?: any;
};

export type ScanningCvScoreResponse = {
  score: number;
  meta_data?: any;
};

export type CandidateDetail = {
  skills: ScanningCvSkill[];
  working_position_histories: ScanningCvWorkingPositionHistories[];
  academic_histories: ScanningCvAcademicHistories[];
};

export type CandidateDetailV3Response = {
  code: number;
  data: {
    summary: string | null;
    basic_information: {
      first_name: string | null;
      middle_name: string | null;
      last_name: string | null;
      full_name_in_local_language: string | null;
      preferred_name: string | null;
      date_of_birth: string | null;
      email_address: string | null;
      phone_number: {
        country_code: string | null;
        number: string | null;
      } | null;
      social_media_channels:
        | {
            channel: string | null;
            url: string | null;
          }[]
        | [];
      coding_channels:
        | {
            channel: string | null;
            url: string | null;
          }[]
        | [];
      portfolio_channels:
        | {
            channel: string | null;
            url: string | null;
          }[]
        | [];
    } | null;
    experience_work:
      | {
          company_name: string | null;
          position: string | null;
          start_date: string | null;
          end_date: string | null;
          country: string | null;
          province_city: string | null;
          commitment: string | null;
          work_arrangement: string | null;
          technical_skills: string[] | [];
          description: string | null;
        }[]
      | [];
    experience_projects:
      | {
          project_name: string | null;
          start_date: string | null;
          end_date: string | null;
          description: string | null;
        }[]
      | [];
    recognition_publications:
      | {
          title: string | null;
          publication_date: string | null;
          link: string | null;
        }[]
      | [];
    recognition_awards:
      | {
          title: string | null;
          date: string | null;
        }[]
      | [];
    skills_technical: string[] | [];
    skills_languages:
      | {
          language: string | null;
          proficiency: string | null;
        }[]
      | [];
    skills_certifications:
      | {
          name: string | null;
          issuing_organization: string | null;
          issue_date: string | null;
          expiration_date: string | null;
          link: string | null;
        }[]
      | [];
    education:
      | {
          university_name: string | null;
          degree: string | null;
          major: string | null;
          start_date: string | null;
          end_date: string | null;
          gpa: number | null;
          degree_status: string | null;
        }[]
      | [];
  };
};
