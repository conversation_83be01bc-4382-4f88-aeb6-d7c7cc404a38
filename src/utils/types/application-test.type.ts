import { InterviewQuestion } from '@prisma/client';

export type JobSkillsParam = {
  jobDescription: string;
};

export type JobSkills = {
  skillName: string;
  id: string;
}[];

export type JobSkillsResponse = {
  code: number;
  message: string;
  data: JobSkills;
};

export type TechniqueQuestionsParam = {
  jobExperienceLevel: string;
  jobSkills: JobSkills;
};

export type CodingChallengeQuestionsParam = {
  jobExperienceLevel: string;
};

export type TechniqueQuestionsResponse = InterviewQuestion & {
  technicalTestId?: number;
};

export type CreateConversationResponse = {
  code: number;
  message: string;
  data: {
    conversation_id: string;
    conversation_name: string;
    user_id: string;
    created_at: string;
    is_finished: boolean;
  };
};
