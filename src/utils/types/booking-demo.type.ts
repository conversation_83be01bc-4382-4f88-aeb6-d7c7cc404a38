export type GoogleCredentials = {
  installed: {
    client_id: string;
    project_id: string;
    auth_uri: string;
    token_uri: string;
    auth_provider_x509_cert_url: string;
    client_secret: string;
    redirect_uris: string[];
  };
};
export type GoogleToken = {
  token: string;
  refresh_token: string;
  token_uri: string;
  client_id: string;
  client_secret: string;
  scopes: string[];
  universe_domain: string;
  account: string;
  expiry: string;
};

export type BusyTime = {
  startTime: string;
  endTime: string;
  meetingUserId: number;
};
