export type EnglishScoringResponse = {
  code: number;
  message: string;
  data: {
    record_id: string;
  };
};

export type EnglishAudioScoringResponse = {
  code: number;
  message: string;
  data: {
    record_id: string;
    result: {
      summary: {
        pron_score: number;
        accuracy_score: number;
        completeness_score: number;
        fluency_score: number;
        prosody_score: number;
      };
      content_score: {
        vocabulary_score: number;
        grammar_score: number;
        topic_score: number;
      };
      text: string;
      overall_score: number;
      overall_result: string;
    };
  };
};

export type TechnicalScoringResponse = {
  code: number;
  message: string;
  data: {
    score: string;
    reason: string;
    suggestion_answer: string;
  };
};

export type AiBotScoringResponse = {
  code: number;
  message: string;
  data: {
    id: string;
    conversation_id: string;
    question: string;
    answer: string;
    score: number;
    reason: string;
    suggestion: string;
    created_at: string;
  }[];
};
