//eslint-disable-next-line
require('dotenv').config();

export const JWT_SECRET = process.env.JWT_SIGNATURE;
export const JWT_EXPIRY_SECONDS = 3600;

export enum ROLES_ENUM {
  ADMIN = 'admin',
  USER = 'user',
}

export const ROLES = {
  ADMIN: 'admin',
  USER: 'user',
};
export const DEFAULT_PAGE_LIMIT = 10;
export const MAX_PAGE_LIMIT = 100;

export const DEFAULT_SORT_BY = 'id';

export const API_PREFIX = '/api/v1';

//Regex
export const PHONE_REGEX = /^[0-9\s+-.()]+$/;

export const SLUG_SEPARATOR = '-';

const linkMap = {
  development: process.env.JOB_DEV_URL,
  production: process.env.JOB_PROD_URL,
  default: process.env.JOB_LOCAL_URL,
};

const linkLandingMap = {
  development: process.env.LANDING_DEV_URL,
  production: process.env.LANDING_PROD_URL,
  default: process.env.LANDING_DEV_URL,
};

export function getEnvironmentLink(): string {
  const environment = process.env.NODE_ENV || 'local';
  return linkMap[environment] || linkMap.default;
}

export function getEnvironmentLandingLink(): string {
  const environment = process.env.NODE_ENV || 'local';
  return linkLandingMap[environment] || linkLandingMap.default;
}
