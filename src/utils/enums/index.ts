export enum ApplicationStatus {
  APPLICATION_PENDING = 'APPLICATION_PENDING',
  APPLICATION_REJECTED = 'APPLICATION_REJECTED',
  APPLICATION_APPROVED = 'APPLICATION_APPROVED',
}

export enum ApplicationTask {
  PENDING_APPLICATION_TASK = 'PENDING_APPLICATION_TASK',
}

export enum NotificationTask {
  PENDING_FOUNDATION_TEST_NOTIFICATION_TASK = 'PENDING_FOUNDATION_TEST_NOTIFICATION_TASK',
  PENDING_TEST_RESULT_NOTIFICATION_TASK = 'PENDING_TEST_RESULT_NOTIFICATION_TASK',
  SCHEDULE_MEETING_NOTIFICATION_TASK = 'SCHEDULE_MEETING_NOTIFICATION_TASK',
  CANCEL_MEETING_NOTIFICATION_TASK = 'CANCEL_MEETING_NOTIFICATION_TASK',
  CV_SCANNING_NOTIFICATION_TASK = 'CV_SCANNING_NOTIFICATION_TASK',
  REMINDER_EMAIL_NOTIFICATION_TASK = 'REMINDER_EMAIL_NOTIFICATION_TASK',
  CONTACT_SUPPORT_NOTIFICATION_TASK = 'CONTACT_SUPPORT_NOTIFICATION_TASK',
}

export enum BookingDemoTask {
  SYNC_EVENT_TASK = 'SYNC_EVENT_TASK',
  SCHEDULE_MEETING_TASK = 'SCHEDULE_MEETING_TASK',
  WATCH_EVENT_TASK = 'WATCH_EVENT_TASK',
  CANCEL_EVENT_TASK = 'CANCEL_EVENT_TASK',
  GET_EVENT_TASK = 'GET_EVENT_TASK',
}

export enum MasterFormTask {
  MASTER_FORM_SCAN_TASK = 'MASTER_FORM_SCAN_TASK',
  MASTER_FORM_AI_BOT_TEST_TASK = 'MASTER_FORM_AI_BOT_TEST_TASK',
  MASTER_FORM_AI_BOT_TEST_RECORDING_TASK = 'MASTER_FORM_AI_BOT_TEST_RECORDING_TASK',
  MASTER_FORM_CODING_TEST_TASK = 'MASTER_FORM_CODING_TEST_TASK',
  MASTER_FORM_ENGLISH_TEST_TASK = 'MASTER_FORM_ENGLISH_TEST_TASK',
  MASTER_FORM_COMPLETION_TASK = 'MASTER_FORM_COMPLETION_TASK',
}

export enum TestStatus {
  TEST_PENDING = 'TEST_PENDING',
  TEST_COMPLETED = 'TEST_COMPLETED',
  TEST_NOTIFIED = 'TEST_NOTIFIED',
  TEST_PASSED = 'TEST_PASSED',
  TEST_FAILED = 'TEST_FAILED',
  GREY_ZONE = 'GREY_ZONE',
}

export enum TestTask {
  APPLICATION_TEST_TASK = 'APPLICATION_TEST_TASK',
  APPLICATION_TEST_V2_TASK = 'APPLICATION_TEST_V2_TASK',
  GET_JOB_SKILLS_TASK = 'GET_JOB_SKILLS_TASK',
}

export enum TestScoringTask {
  ENGLISH_TEST_SCORING_TASK = 'ENGLISH_TEST_SCORING_TASK',
  TECHNICAL_TEST_SCORING_TASK = 'TECHNICAL_TEST_SCORING_TASK',
  AI_BOT_TEST_SCORING_TASK = 'AI_BOT_TEST_SCORING_TASK',
  CODING_TEST_SCORING_TASK = 'CODING_TEST_SCORING_TASK',
  TEST_RESULT_TASK = 'TEST_RESULT_TASK',
}

export enum CvScanningTask {
  CV_SCANNING_TASK = 'CV_SCANNING_TASK',
  CV_SCANNING_ALL_CANDIDATES_TASK = 'CV_SCANNING_ALL_CANDIDATES_TASK',
  CV_SCANNING_CANCEL_TASK = 'CV_SCANNING_CANCEL_TASK',
}

export enum InteviewTask {
  SCORING_HANDLING_TASK = 'SCORING_HANDLING_TASK'
}

export enum CvStatus {
  CV_SENT_TO_HR = 'CV_SENT_TO_HR',
  CV_SENT_TO_CLIENT = 'CV_SENT_TO_CLIENT',
}

export enum HrReviewCvStatus {
  HR_REVIEW_PENDING = 'HR_REVIEW_PENDING',
  HR_REVIEW_APPROVED = 'HR_REVIEW_APPROVED',
  HR_REVIEW_REJECTED = 'HR_REVIEW_REJECTED',
}

export enum ClientReviewCvStatus {
  CLIENT_REVIEW_PENDING = 'CLIENT_REVIEW_PENDING',
  CLIENT_APPROVED_FOR_INTERVIEW = 'CLIENT_APPROVED_FOR_INTERVIEW',
  CLIENT_REJECTED = 'CLIENT_REJECTED',
}

export enum InterviewStatus {
  INTERVIEW_SCHEDULED = 'INTERVIEW_SCHEDULED',
  INTERVIEW_COMPLETED = 'INTERVIEW_COMPLETED',
}

export enum OfferStatus {
  OFFER_MADE = 'OFFER_MADE',
  NO_OFFER_MADE = 'NO_OFFER_MADE',
}

export enum RejectReasons {
  HR_REVIEW_REJECTED = 'HR_REVIEW_REJECTED',
  CLIENT_REJECTED = 'CLIENT_REJECTED',
  TEST_FAILED = 'TEST_FAILED',
}

export enum QueueName {
  APPLICATION_QUEUE = 'APPLICATION_QUEUE',
  APPLICATION_TEST_QUEUE = 'APPLICATION_TEST_QUEUE',
  NOTIFICATION_QUEUE = 'NOTIFICATION_QUEUE',
  TEST_SCORING_QUEUE = 'TEST_SCORING_QUEUE',
  BOOKING_DEMO_QUEUE = 'BOOKING_DEMO_QUEUE',
  CV_SCANNING_QUEUE = 'CV_SCANNING_QUEUE',
  MASTER_FORM_QUEUE = 'MASTER_FORM_QUEUE',
  SCORING_HANDLING_QUEUE = 'SCORING_HANDLING_QUEUE',
}

export enum ExperienceLevel {
  ENTRY = 'Entry',
  Intern = 'Intern',
  Fresher = 'Fresher',
  JUNIOR = 'Junior',
  MIDDLE = 'Middle',
  SENIOR = 'Senior',
  EXPERT = 'Expert',
}

export enum CodingChallengeLevel {
  EASY = 'Easy',
  MEDIUM = 'Medium',
  HARD = 'Hard',
}

export enum EmailStructure {
  EMAIL_SUBJECT = 'EMAIL_SUBJECT',
  EMAIL_TEXT = 'EMAIL_TEXT',
  EMAIL_TITLE = 'EMAIL_TITLE',
  EMAIL_HTML = 'EMAIL_HTML',
  EMAIL_BUTTON = 'EMAIL_BUTTON',
}

export enum EmailType {
  FOUNDATION_TEST = 'FOUNDATION_TEST',
  TEST_RESULT = 'TEST_RESULT',
  SCHEDULE_MEETING = 'SCHEDULE_MEETING',
  CANCEL_MEETING = 'CANCEL_MEETING',
  CV_SCANNING = 'CV_SCANNING',
  REMINDER_EMAIL = 'REMINDER_EMAIL',
  CONTACT_SUPPORT = 'CONTACT_SUPPORT',
}

export enum Language {
  EN = 'en',
  JA = 'ja',
}

export enum EnglishScoringMlResponse {
  PASS = 'Pass',
  FAIL = 'Fail',
  GREY_ZONE = 'Grey_zone',
}
