import { Job } from 'bull';
import {
  EnglishTest,
  TechnicalTest,
  FoundationTest,
  TechnicalTestQuestion,
  InterviewQuestion,
  AIBotTest,
  CodingTest,
  CodingQuestion,
} from '@prisma/client';

export interface ITaskTestScoringConsumer {
  processEnglishTestScoring(job: Job<ITestScoring>): Promise<void>;
  processTechnicalTestScoring(job: Job<ITestScoring>): Promise<void>;
  processAIBotTestScoring(job: Job<ITestScoring>): Promise<void>;
  processCodingTestScoring(job: Job<ITestScoring>): Promise<void>;
  processTestResult(job: Job<ITestScoring>): Promise<void>;
}

export interface ITestScoringService {
  // englishTestScoring(): Promise<void>;
  // technicalTestScoring(): Promise<void>;
  // aiBotTestScoring(): Promise<void>;
  // codingTestScoring(): Promise<void>;
  // testResult(): Promise<void>;
}

export interface ITechnicalTestQuestion extends TechnicalTestQuestion {
  question: InterviewQuestion;
}

export interface ITechnicalTest extends TechnicalTest {
  technicalTestQuestions: ITechnicalTestQuestion[];
}

export interface ICodingTest extends CodingTest {
  question: CodingQuestion[];
}

export interface ITestResult extends FoundationTest {
  technicalTest: TechnicalTest;
  englishTest: EnglishTest;
  aiBotTest: AIBotTest;
  codingTest: CodingTest;
}

export interface ITestScoring {
  englishTest?: EnglishTest;
  aiBotTest?: AIBotTest;
  technicalTest?: ITechnicalTest;
  codingTest?: ICodingTest;
  foundationTest?: FoundationTest;
  testResult?: ITestResult;
}
