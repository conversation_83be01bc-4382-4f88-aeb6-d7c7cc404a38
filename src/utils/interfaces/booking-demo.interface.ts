import { Job } from 'bull';
import { MeetingUser, Meeting } from '@prisma/client';

export interface ITaskBookingDemoConsumer {
  procesSyncEvents(job: Job<IBookingDemo>): Promise<void>;
  // processWatchEvents(job: Job<IBookingDemo>): Promise<void>;
  // processCancelEvent(job: Job<IBookingDemo>): Promise<void>;
  processGetEvents(job: Job<IBookingDemo>): Promise<any>;
}

export interface IBookingDemoService {
  syncEvents(): Promise<void>;
  // watchEvents(channelId: string): Promise<void>;
  // cancelEvent(meetingId: number): Promise<void>;
  getEvents(salePersonId: number): Promise<any>;
}

interface IMeeting extends Meeting {
  MeetingUser_Meeting_customerIdToMeetingUser: MeetingUser;
  MeetingUser_Meeting_hostIdToMeetingUser: MeetingUser;
}

export interface IBookingDemo {
  salePersons?: MeetingUser[];
  salePerson?: MeetingUser;
  meeting?: IMeeting;
}
