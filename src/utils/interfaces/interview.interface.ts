// import { Message } from '@prisma/client';
import { AIBotTestRoom } from '@prisma/client';
import { Job } from 'bull';

export interface IHandleScoring {
  room: AIBotTestRoom;
}


export interface IHandleScoringService {
  scanAndStore(): Promise<void>;
}


export interface IHandleScoringConsumer {
  processRoom(job: Job<IHandleScoring>): Promise<void>;
}

export interface MeetingTranscription {
  role: string;
  content: string;
  created_at: string;
}

export interface MeetingScoringInput {
  fluencyAndCoherence?: string;
  lexicalResource?: string;
  grammaticalRangeAndAccuracy?: string;
  CEFRStandard?: string;
  aIBotTestRoomId: number;
  feedbacks: MeetingFeedbackInput[];
}

export interface MeetingFeedbackInput {
  aiAssessment?: string;
  scoringLevel?: string;
  question?: string;
  idealAnswer?: string;
  userAnswer?: string;
}
