import {
  Application,
  Job as JobApplication,
  User,
  UserRole,
  Profile,
  JobSkill,
  SkillLevel,
  FoundationTest,
} from '@prisma/client';
import { Job } from 'bull';

import {
  JobSkillsParam,
  JobSkillsResponse,
  TechniqueQuestionsParam,
  TechniqueQuestionsResponse,
  CodingChallengeQuestionsParam,
} from '../types/application-test.type';

export interface ITaskApplicationTestConsumer {
  processApplicationTest(job: Job<IApplicationTest>): Promise<void>;
  processApplicationTestV2(job: Job<IApplicationTest>): Promise<void>;
  getJobSkills(jobSkillsParam: JobSkillsParam): Promise<JobSkillsResponse>;
  getTechniqueQuestions(
    techniqueQuestionsParam: TechniqueQuestionsParam,
  ): Promise<TechniqueQuestionsResponse[]>;
  getCodingChallengeQuestions(
    codingChallengeQuestionsParam: CodingChallengeQuestionsParam,
  ): Promise<TechniqueQuestionsResponse>;
}

export interface IApplicationTestService {
  // applicationTest(): Promise<void>;
  // applicationTestV2(): Promise<void>;
  // jobSkills(job: Job): Promise<void>;
}

interface IJobSkill extends JobSkill {
  skillLevel: SkillLevel;
}

interface IJob extends JobApplication {
  jobSkillLevel: IJobSkill[];
}

export interface IUserRole extends UserRole {
  user: User;
  profile?: Profile;
}

export interface IApplication extends Application {
  job: IJob;
  userRole?: IUserRole;
  foundationTest?: FoundationTest;
}

export interface IApplicationTest {
  application: IApplication;
}
