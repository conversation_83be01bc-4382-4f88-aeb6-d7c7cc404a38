import { Job } from 'bull';
import {
  FoundationTest,
  TechnicalTest,
  EnglishTest,
  Application,
  Job as ApplicationJob,
  UserRole,
  User,
  Profile,
  CodingTest,
  AIBotTest,
  Meeting,
  MeetingUser,
  CVTask,
  CVTaskCandidate,
  AcademyHistory,
  EmploymentHistory,
  WorkingPositionHistory,
  ContactSupport,
} from '@prisma/client';

interface IUserRole extends UserRole {
  user: User;
  profile?: Profile;
}

interface IApplication extends Application {
  job: ApplicationJob;
  userRole: IUserRole;
}

export interface ITaskNotificationConsumer {
  processPendingFoundationTestNotification(
    job: Job<INotification>,
  ): Promise<void>;

  processPendingTestResultNotification(job: Job<INotification>): Promise<void>;
  procesScheduleMeetingNotification(job: Job<INotification>): Promise<void>;
  processCancelMeetingNotification(job: Job<INotification>): Promise<void>;
  processCvScanningNotification(job: Job<INotification>): Promise<void>;
  proceReminderMeetingNotification(job: Job<INotification>): Promise<void>;
  processContactSupportNotification(job: Job<INotification>): Promise<void>;
}

export interface INotificationService {
  // pendingFoundationTestNotification(): Promise<void>;
  // pendingTestResultNotification(): Promise<void>;
  scheduleMeetingNotification(): Promise<void>;
  cancelMeetingNotification(): Promise<void>;
  reminderMeetingNotification(): Promise<void>;
  contactSupportNotification(): Promise<void>;
}

export interface IFoundationTest extends FoundationTest {
  technicalTest: TechnicalTest;
  englishTest: EnglishTest;
  codingTest: CodingTest;
  aiBotTest: AIBotTest;
  application: IApplication;
}

export interface IMeeting extends Meeting {
  MeetingUser_Meeting_customerIdToMeetingUser: MeetingUser;
  MeetingUser_Meeting_hostIdToMeetingUser: MeetingUser;
  meetingLink?: string;
}

interface IEmploymentHistory extends EmploymentHistory {
  workingPositionHistory: WorkingPositionHistory[];
}

interface IProfile extends Profile {
  academy: AcademyHistory[];
  employment: IEmploymentHistory[];
}

export interface ICVTaskCandidate extends CVTaskCandidate {
  Profile: IProfile;
}

export interface ICVTask extends CVTask {
  candidates: ICVTaskCandidate[];
  job: ApplicationJob;
}

interface IContactSupport extends ContactSupport {}

export interface INotification {
  foundationTest?: IFoundationTest;
  meeting?: IMeeting;
  cvTask?: ICVTask;
  contactSupport?: IContactSupport;
}
