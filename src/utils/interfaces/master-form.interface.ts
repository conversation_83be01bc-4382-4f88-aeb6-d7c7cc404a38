import {
  ResumeUploadSection,
  CodingTestSection,
  AIBotTestSection,
  EnglishTestSection,
  MasterForm,
  WorkPreferenceSection,
  AIBotTestRoom,
} from '@prisma/client';

export interface IMasterFormService {}

interface IMasterFormCompleted extends MasterForm {
  resume?: ResumeUploadSection;
  workPreference?: WorkPreferenceSection;
}

export interface IMasterForm {
  resume?: ResumeUploadSection;
  codingTest?: CodingTestSection;
  aiBotTest?: AIBotTestSection;
  aiBotTestRooms?: AIBotTestRoom[];
  englishTest?: EnglishTestSection;
  masterFormCompleted?: IMasterFormCompleted;
}

export interface ITaskMaterFormConsumer {}
