import { Job } from 'bull';
import {
  CVTask,
  CVTaskCandidate,
  Job as JobCv,
  Profile,
  AcademyHistory,
  EmploymentHistory,
} from '@prisma/client';

export interface ICvScanningConsumer {
  processScanningCv(job: Job): Promise<void>;
  // processCancelScanningCv(job: Job): Promise<void>;
  downloadFileFromS3(s3Url: string, downloadPath: string): Promise<any>;
}

export interface ICvScanningService {
  // pendingApplication(): Promise<void>;
  // cvScanning(): Promise<void>;
  // cancelCvScanning(): Promise<void>;
}

interface IProfile extends Profile {
  academy: AcademyHistory[];
  employment: EmploymentHistory[];
}

export interface ICVTaskCandidate extends CVTaskCandidate {
  Profile: IProfile;
}

export interface ICVTask extends CVTask {
  candidates: ICVTaskCandidate[];
  job: JobCv;
}

export interface ICvScanning {
  cVTask: ICVTask;
}
