import Redis from 'ioredis';

const redisClient = new Redis({
    host: process.env.QUEUE_REDIS_HOST,
    port: Number(process.env.QUEUE_REDIS_PORT),
    username: process.env.QUEUE_REDIS_USER || "default",
    password: process.env.QUEUE_REDIS_PASS,
    db: 0,
});

redisClient.on('connect', () => {
    console.log('Connected to Redis');
});

redisClient.on('error', (error) => {
    console.error('Redis connection error:', error);
});

export default redisClient;
