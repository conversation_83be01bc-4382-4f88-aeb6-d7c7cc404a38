# Use an official Node.js runtime as the base image
FROM node:21-alpine

# Set the working directory inside the container
WORKDIR /usr/src/app

# Copy package.json and yarn.lock to the working directory
COPY package.json yarn.lock ./

# Install dependencies
RUN yarn install

# Copy the rest of the application code to the working directory
COPY . .

# Generate Prisma client
RUN npx prisma generate

# Build the NestJS application
RUN NODE_OPTIONS="--max-old-space-size=4096" yarn build

# Expose the application port (adjust this if your app runs on a different port)
EXPOSE 8888

# Start the application
CMD ["yarn", "start:prod"]